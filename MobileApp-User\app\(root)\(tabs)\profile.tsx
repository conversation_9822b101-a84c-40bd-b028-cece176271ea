import React, { useEffect, useState } from "react";
import { 
  Text, 
  SafeAreaView, 
  TouchableOpacity, 
  View, 
  StatusBar, 
  Image,
  Pressable,
  ActivityIndicator,
  Alert
} from "react-native";
import { useAuth, useUser } from "@clerk/clerk-expo";
import { router } from "expo-router";
import { images } from "@/constants";

interface UserProfile {
  fullName: string;
  mobileNumber: string;
  email: string;
  isVIP: boolean;
}

const ProfileScreen = () => {
  const { signOut } = useAuth();
  const { user } = useUser();
  const [userProfile, setUserProfile] = useState<UserProfile | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  const userImage = user?.imageUrl || null;
  const isEmailUser = !user?.externalAccounts?.length;

  // Fetch user profile from backend
  useEffect(() => {
    const fetchUserProfile = async () => {
      if (!user || !user.primaryEmailAddress) {
        setIsLoading(false);
        return;
      }

      try {
        const email = user.primaryEmailAddress.emailAddress;
        console.log("Fetching profile for email:", email);
        
        const response = await fetch(
          `http://***************:3000/api/customer/profile/${email}`
        );

        if (!response.ok) {
          console.error("Error fetching user profile:", response.status);
          setIsLoading(false);
          return;
        }

        const data = await response.json();
        console.log("User profile data:", data);

        if (data.success && data.user) {
          setUserProfile({
            fullName: data.user.fullName || user?.fullName || "QueueFree User",
            mobileNumber: data.user.mobileNumber || "",
            email: email,
            isVIP: data.user.isVIP || false
          });
        } else {
          // If backend doesn't have the user data, use Clerk data
          setUserProfile({
            fullName: user?.fullName || "QueueFree User",
            mobileNumber: "",
            email: email,
            isVIP: false
          });
        }
      } catch (error) {
        console.error("Error fetching user profile:", error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchUserProfile();
  }, [user]);

  const handleSignOut = async () => {
    try {
      await signOut();
      router.replace("/(auth)/sign-in");
    } catch (error) {
      console.error("Error signing out:", error);
    }
  };

  const handleProfileEdit = () => {
    Alert.alert(
      "Coming Soon",
      "Profile editing will be available in the next update!",
      [{ text: "OK", onPress: () => console.log("OK Pressed") }]
    );
  };

  // const handleViewProfile = () => {
  //   router.push("/(root)/edit-profile");
  // };

  return (
    <SafeAreaView className="flex-1 bg-white">
      <StatusBar barStyle="dark-content" backgroundColor="white" />
      <View className="p-6 mt-5">
        
        {isLoading ? (
          <View className="items-center mb-8">
            <ActivityIndicator size="large" color="#5552E4" />
          </View>
        ) : (
          <View className="items-center mb-8">
            {isEmailUser ? (
              <View className="w-24 h-24 rounded-full bg-secondary-200 items-center justify-center">
                <Image 
                  source={images.camera}
                  className="w-9 h-8"
                  tintColor="#666666"
                />
              </View>
            ) : (
              userImage && (
                <Image 
                  source={{ uri: userImage }}
                  className="w-24 h-24 rounded-full"
                />
              )
            )}
            <Text className="font-poppins-semibold text-lg mt-3">
              {userProfile?.fullName || "QueueFree User"}
            </Text>
            <Text className="font-poppins-regular text-secondary-600 text-sm">
              {userProfile?.email || user?.primaryEmailAddress?.emailAddress}
            </Text>
            {userProfile?.isVIP && (
              <View className="bg-primary-100 px-3 py-1 rounded-full mt-2">
                <Text className="font-poppins-medium text-primary-500 text-xs">VIP Member</Text>
              </View>
            )}
          </View>
        )}

        <View className="space-y-0 -mx-6"> 
          <Pressable 
            className="px-6"  
            android_ripple={{ color: 'rgba(0, 0, 0, 0.1)' }}
            onPress={handleProfileEdit}
          >
            <View className="flex-row items-center justify-between py-6 border-b border-secondary-100">
              <View className="flex-row items-center flex-1">
                <Image source={images.person} className="w-[18px] h-[21px] mr-4" />
                <Text className="font-poppins-regular">Your Profile</Text>
              </View>
              <Image source={images.down} className="w-[16px] h-[10px] rotate-[-90deg]" tintColor="#455A64" />
            </View>
          </Pressable>


          <Pressable 
            className="px-6"
            android_ripple={{ color: 'rgba(0, 0, 0, 0.1)' }}
          >
            <View className="flex-row items-center justify-between py-6 border-b border-secondary-100">
              <View className="flex-row items-center flex-1">
                <Image source={images.language} className="w-[19px] h-[19px] mr-4" />
                <Text className="font-poppins-regular">Language</Text>
              </View>
              <Image source={images.down} className="w-[16px] h-[10px] rotate-[-90deg]" tintColor="#455A64" />
            </View>
          </Pressable>

          <Pressable 
            className="px-6"
            android_ripple={{ color: 'rgba(0, 0, 0, 0.1)' }}
          >
            <View className="flex-row items-center justify-between py-6 border-b border-secondary-100">
              <View className="flex-row items-center flex-1">
                <Image source={images.help} className="w-[19px] h-[20px] mr-4" />
                <Text className="font-poppins-regular">Help Center</Text>
              </View>
              <Image source={images.down} className="w-[16px] h-[10px] rotate-[-90deg]" tintColor="#455A64" />
            </View>
          </Pressable>

          <Pressable 
            className="px-6"
            android_ripple={{ color: 'rgba(0, 0, 0, 0.1)' }}
          >
            <View className="flex-row items-center justify-between py-6 border-b border-secondary-100">
              <View className="flex-row items-center flex-1">
                <Image source={images.privacy} className="w-[16px] h-[20px] mr-4" />
                <Text className="font-poppins-regular">Privacy Policy</Text>
              </View>
              <Image source={images.down} className="w-[16px] h-[10px] rotate-[-90deg]" tintColor="#455A64" />
            </View>
          </Pressable>

          <Pressable 
            className="px-6"
            android_ripple={{ color: 'rgba(0, 0, 0, 0.1)' }}
          >
            <View className="flex-row items-center justify-between py-6 border-b border-secondary-100">
              <View className="flex-row items-center flex-1">
                <Image source={images.terms} className="w-[19.5px] h-[18px] mr-4" />
                <Text className="font-poppins-regular">Terms and Conditions</Text>
              </View>
              <Image source={images.down} className="w-[16px] h-[10px] rotate-[-90deg]" tintColor="#455A64" />
            </View>
          </Pressable>

          <Pressable 
            className="px-6"
            android_ripple={{ color: 'rgba(0, 0, 0, 0.1)' }}
            onPress={handleSignOut}
          >
            <View className="flex-row items-center justify-between py-6">
              <View className="flex-row items-center flex-1">
                <Image 
                  source={images.logout} 
                  className="w-[16px] h-[20px] mr-4"
                  tintColor="#E53E3E"
                />
                <Text className="font-poppins-regular text-danger-600">Log Out</Text>
              </View>
              
            </View>
          </Pressable>
        </View>
      </View>
    </SafeAreaView>
  );
};

export default ProfileScreen;
