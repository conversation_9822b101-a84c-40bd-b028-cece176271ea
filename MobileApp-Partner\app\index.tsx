import { useAuth } from "@clerk/clerk-expo";
import { Redirect } from "expo-router";
import * as SecureStore from 'expo-secure-store';
import { useEffect, useState } from 'react';
import { useUser } from "@clerk/clerk-expo";

interface ServiceStatus {
  serviceName: string | null;
  documents: {
    panNumber: string;
    panCardImage: string;
    gstin?: string;
  } | null;
  termsAccepted: boolean;
}

export default function Page() {
  const { isSignedIn } = useAuth();
  const { user, isLoaded: userLoaded } = useUser();
  const [isLoading, setIsLoading] = useState(true);
  const [serviceStatus, setServiceStatus] = useState<ServiceStatus | null>(null);

  useEffect(() => {
    const checkServiceStatus = async () => {
      try {
        if (!userLoaded || !isSignedIn || !user?.primaryEmailAddress?.emailAddress) {
          setIsLoading(false);
          return;
        }

        const email = encodeURIComponent(user.primaryEmailAddress.emailAddress);
        const response = await fetch(
          `http://***************:3000/api/partner/service-details/${email}`
        );

        if (!response.ok) {
          throw new Error('Failed to fetch service status');
        }

        const data = await response.json();
        
        setServiceStatus({
          serviceName: data.serviceName,
          documents: data.documents,
          termsAccepted: data.termsAccepted
        });
      } catch (error) {
        console.error('Error checking service status:', error);
        setServiceStatus(null);
      } finally {
        setIsLoading(false);
      }
    };

    checkServiceStatus();
  }, [isSignedIn, userLoaded, user]);

  // Show loading state
  if (!userLoaded || isLoading) return null;

  // Handle authentication
  if (!isSignedIn) {
    return <Redirect href="/(auth)/sign-in" />;
  }

  // Add a helper function to validate documents
  const hasValidDocuments = (status: ServiceStatus | null) => {
    return status?.documents && 
           status.documents.panNumber && 
           status.documents.panCardImage;
  };

  // If service name is missing, redirect to step1
  if (!serviceStatus?.serviceName) {
    return <Redirect href="/service-setup/step1" />;
  }

  // If service has name but no valid documents, redirect to step2
  if (!hasValidDocuments(serviceStatus)) {
    return <Redirect href="/service-setup/step2" />;
  }

  // If both service name and valid documents exist, go to step3 or dashboard
  if (serviceStatus.serviceName && hasValidDocuments(serviceStatus)) {
    if (serviceStatus.termsAccepted) {
      return <Redirect href="/(root)/(tabs)/dashboard" />;
    } else {
      return <Redirect href="/service-setup/step3" />;
    }
  }

  // Fallback to step1 if no conditions are met
  return <Redirect href="/service-setup/step1" />;
}
