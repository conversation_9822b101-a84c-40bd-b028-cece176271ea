import React from "react";
import {
  Text,
  SafeAreaView,
  View,
  TextInput,
  Alert,
  TouchableOpacity,
  Image,
  Keyboard,
} from "react-native";
import { useSignIn, useSignUp,useUser  } from "@clerk/clerk-expo";
import { useEffect } from 'react';
import { router, useLocalSearchParams } from "expo-router";
import ButtonBlue from "@/components/ButtonBlue";
import { images } from "@/constants";
import axios from 'axios';
import * as SecureStore from "expo-secure-store";

const VerifyOtpScreen = () => {
  const [otp, setOtp] = React.useState("");
  const [loading, setLoading] = React.useState(false);
  const { signIn, setActive } = useSignIn();
  const { signUp } = useSignUp();
  const { email, isSignUp } = useLocalSearchParams();
  const inputRefs = React.useRef<Array<TextInput | null>>([]);
  const { user, isLoaded } = useUser();
  const [isRegistered, setIsRegistered] = React.useState(false);

  useEffect(() => {
    if (isLoaded && user && isSignUp === "true" && !isRegistered) {
      sendUserData(email as string).then(() => {
        setIsRegistered(true);
      });
    }
  }, [isLoaded, user, isSignUp, email, isRegistered]);

  interface RegistrationResponse {
    status: string;
    data?: {
      serviceId: number;
    };
  }
  
  const sendUserData = async (userEmail: string) => {
      try {
        const response = await axios.post<RegistrationResponse>(
          "http://192.168.250.149:3000/api/auth/register-partner",
          { email: userEmail },
          { 
            headers: { 
              "Content-Type": "application/json",
              "Accept": "application/json"
            } 
          }
        );
  
        console.log("Registration response:", response.data);
        
        // Store service ID if available
        if (response.data?.data?.serviceId) {
          await SecureStore.setItemAsync('serviceId', response.data.data.serviceId.toString());
        }
        
        return response.data.status === 'success';
    } catch (error: any) {
      console.error("Registration error:", error.response?.data || error.message);
      return false;
    }
  };

  const checkServiceDetails = async (email: string) => {
    try {
      const encodedEmail = encodeURIComponent(email);
      const response = await fetch(
        `http://192.168.250.149:3000/api/partner/service-details/${encodedEmail}`
      );
  
      if (response.ok) {
        const data = await response.json();
        return data;
      }
      return null;
    } catch (error) {
      console.error('Error checking service details:', error);
      return null;
    }
  };

  // Add this new helper function
  const checkServiceCompletion = async (email: string) => {
    try {
      const encodedEmail = encodeURIComponent(email);
      const response = await fetch(
        `http://192.168.250.149:3000/api/partner/service-completion/${encodedEmail}`
      );
      
      if (response.ok) {
        const data = await response.json();
        return data;
      }
      return null;
    } catch (error) {
      console.error('Error checking service completion:', error);
      return null;
    }
  };

  const onVerifyOtp = async () => {
    if (!signIn || !signUp) {
      Alert.alert("Error", "Authentication service is not available.");
      return;
    }

    if (!otp || otp.length !== 6) {
      Alert.alert("Error", "Please enter a valid code");
      return;
    }

    try {
      setLoading(true);

      if (isSignUp === "true") {
        const signUpAttempt = await signUp.attemptEmailAddressVerification({
          code: otp,
        });
        
        await setActive({ session: signUpAttempt.createdSessionId });
        
        // Initialize service status for new users
        await SecureStore.setItemAsync('serviceStatus', JSON.stringify({
          infoCompleted: false,
          submitted: false,
          verificationStatus: null,
          termsAccepted: false
        }));

        router.replace("/service-setup/step1");
      } else {
        const signInAttempt = await signIn.attemptFirstFactor({
          strategy: "email_code",
          code: otp,
        });
        
        await setActive({ session: signInAttempt.createdSessionId });

        // Check complete service status
        const serviceStatus = await checkServiceCompletion(email as string);
        
        if (serviceStatus) {
          // Store service data
          if (serviceStatus.serviceId) {
            await SecureStore.setItemAsync('serviceId', serviceStatus.serviceId.toString());
          }
          
          await SecureStore.setItemAsync('serviceStatus', JSON.stringify({
            verificationStatus: serviceStatus.verificationStatus,
            termsAccepted: serviceStatus.termsAccepted
          }));

          // Redirect based on service status
          if (serviceStatus.exists && 
              serviceStatus.isVerified && 
              serviceStatus.termsAccepted) {
            router.replace("/(root)/(tabs)/dashboard");
            return;
          }
          
          if (serviceStatus.exists && 
              !serviceStatus.isVerified) {
            router.replace("/service-setup/step3");
            return;
          }
        }

        // Default to step1 if no status or incomplete
        router.replace("/service-setup/step1");
      }
    } catch (err: any) {
      Alert.alert("Error", err.message || "Invalid verification code");
    } finally {
      setLoading(false);
    }
  };

  // Modified OTP input handling for web compatibility
  const handleOtpChange = (index: number, value: string) => {
    if (value.length > 1) {
      // Handle paste
      const pastedOtp = value.slice(0, 6);
      setOtp(pastedOtp);
      
      // Focus last input if complete
      if (pastedOtp.length === 6 && inputRefs.current[5]) {
        inputRefs.current[5]?.focus?.();
      }
    } else {
      // Handle single character input
      const newOtp = otp.split('');
      newOtp[index] = value;
      setOtp(newOtp.join(''));

      // Move focus forward
      if (value && index < 5 && inputRefs.current[index + 1]) {
        inputRefs.current[index + 1]?.focus?.();
      }
    }
  };

  return (
    <SafeAreaView className="flex-1 bg-white justify-center items-center">
      <View className="flex-1 px-8 pt-12 items-center">
        <View className="w-full flex-row  mt-4">
          <TouchableOpacity
            className="flex-row items-center absolute left-0 border border-secondary-600 w-12 h-12 rounded-full justify-center"
            onPress={() => router.back()}
          >
            <Image source={images.back} className="w-4 h-4" />
          </TouchableOpacity>
          <View className="flex-1 items-center ">
            <Text className="font-poppins-medium text-4xl mb-2">
              Verify Email
            </Text>
            <Text className="font-poppins-regular text-secondary-600">
              Enter the code sent to
            </Text>
            <Text className="font-poppins-medium text-secondary-500 mb-14">
              {email}
            </Text>
          </View>
        </View>

        <View className="flex-row gap-4 mb-8">
          {[...Array(6)].map((_, index) => (
            <TextInput
              key={index}
              ref={(ref) => (inputRefs.current[index] = ref)}
              className={`h-[55px] w-[50px] border rounded-xl text-lg font-poppins-regular text-center ${
                otp[index] ? "border-primary-500" : "border-secondary-400"
              }`}
              value={otp[index] || ""}
              onChangeText={(value) => handleOtpChange(index, value)}
              keyboardType="numeric"
              maxLength={6}
              onKeyPress={({ nativeEvent }) => {
                if (nativeEvent.key === "Backspace" && index > 0) {
                  const newOtp = otp.split('');
                  newOtp[index] = '';
                  setOtp(newOtp.join(''));
                  inputRefs.current[index - 1]?.focus?.();
                }
              }}
            />
          ))}
        </View>

        <ButtonBlue
          label={loading ? "Verifying..." : "Verify"}
          onPress={onVerifyOtp}
          disabled={loading}
          bgVariant="primary"
          textVariant="primary"
        />
      </View>
    </SafeAreaView>
  );
};

export default VerifyOtpScreen;
