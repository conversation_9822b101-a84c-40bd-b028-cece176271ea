"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CustomerService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const user_entity_1 = require("./user.entity");
const queue_entity_1 = require("../partner/entities/queue.entity");
const service_entity_1 = require("../partner/entities/service.entity");
const service_setup_entity_1 = require("../partner/entities/service-setup.entity");
const redis_service_1 = require("../services/redis/redis.service");
const queue_flow_service_1 = require("../services/queue-flow/queue-flow.service");
const core_1 = require("@nestjs/core");
let CustomerService = class CustomerService {
    constructor(userRepository, queueRepository, serviceRepository, serviceSetupRepository, redisService, moduleRef) {
        this.userRepository = userRepository;
        this.queueRepository = queueRepository;
        this.serviceRepository = serviceRepository;
        this.serviceSetupRepository = serviceSetupRepository;
        this.redisService = redisService;
        this.moduleRef = moduleRef;
        this.queueFlowService = null;
    }
    async getQueueFlowService() {
        if (this.queueFlowService) {
            return this.queueFlowService;
        }
        try {
            this.queueFlowService = await this.moduleRef.resolve(queue_flow_service_1.QueueFlowService);
            return this.queueFlowService;
        }
        catch (error) {
            console.error('Error resolving QueueFlowService:', error);
            return null;
        }
    }
    hasTimeSlotEnded(queueDate, timeSlot, buffer = 0) {
        try {
            let queueDateObj;
            if (queueDate instanceof Date) {
                queueDateObj = queueDate;
            }
            else if (typeof queueDate === 'string') {
                queueDateObj = new Date(queueDate);
            }
            else {
                queueDateObj = new Date();
            }
            const now = new Date();
            const queueYear = queueDateObj.getFullYear();
            const queueMonth = queueDateObj.getMonth();
            const queueDay = queueDateObj.getDate();
            const nowYear = now.getFullYear();
            const nowMonth = now.getMonth();
            const nowDay = now.getDate();
            if (queueYear < nowYear ||
                (queueYear === nowYear && queueMonth < nowMonth) ||
                (queueYear === nowYear && queueMonth === nowMonth && queueDay < nowDay)) {
                console.log(`Queue date ${queueDateObj.toISOString().split('T')[0]} is before today ${now.toISOString().split('T')[0]} - ended`);
                return true;
            }
            if (queueYear > nowYear ||
                (queueYear === nowYear && queueMonth > nowMonth) ||
                (queueYear === nowYear && queueMonth === nowMonth && queueDay > nowDay)) {
                console.log(`Queue date ${queueDateObj.toISOString().split('T')[0]} is after today ${now.toISOString().split('T')[0]} - not ended`);
                return false;
            }
            console.log(`Queue date ${queueDateObj.toISOString().split('T')[0]} is today - checking time slot`);
            let timeEnd = '';
            if (timeSlot && timeSlot.includes(' - ')) {
                timeEnd = timeSlot.split(' - ')[1].trim();
            }
            else if (timeSlot) {
                timeEnd = timeSlot.trim();
            }
            else {
                console.log('No time slot provided, cannot determine if ended');
                return false;
            }
            console.log(`Time slot end time: ${timeEnd}`);
            let hours = 0;
            let minutes = 0;
            if (timeEnd.includes('AM') || timeEnd.includes('PM')) {
                const isPM = timeEnd.includes('PM');
                const timeParts = timeEnd.replace(/\s?(AM|PM)/, '').split(':');
                hours = parseInt(timeParts[0], 10);
                if (isPM && hours < 12)
                    hours += 12;
                if (!isPM && hours === 12)
                    hours = 0;
                minutes = parseInt(timeParts[1] || '0', 10);
            }
            else {
                const [hoursStr, minutesStr] = timeEnd.split(':');
                hours = parseInt(hoursStr, 10);
                minutes = parseInt(minutesStr || '0', 10);
            }
            const timeSlotEndTime = new Date(queueDateObj);
            timeSlotEndTime.setHours(hours, minutes, 0, 0);
            if (buffer > 0) {
                timeSlotEndTime.setMinutes(timeSlotEndTime.getMinutes() + buffer);
            }
            const hasEnded = now > timeSlotEndTime;
            console.log(`Current time: ${now.toLocaleTimeString()}, End time: ${timeSlotEndTime.toLocaleTimeString()}, Has ended: ${hasEnded}`);
            return hasEnded;
        }
        catch (error) {
            console.error(`Error in hasTimeSlotEnded: ${error.message}`, error);
            return false;
        }
    }
    async createUser(email, fullName, clerkId) {
        if (!email || typeof email !== 'string') {
            throw new common_1.BadRequestException('Valid email is required');
        }
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(email)) {
            throw new common_1.BadRequestException('Invalid email format');
        }
        try {
            console.log('Checking for existing user:', email);
            let user = await this.userRepository.findOne({ where: { email } });
            if (user) {
                let updated = false;
                if (clerkId && !user.clerkId) {
                    user.clerkId = clerkId;
                    updated = true;
                }
                if (fullName && !user.fullName) {
                    user.fullName = fullName;
                    updated = true;
                }
                if (updated) {
                    user = await this.userRepository.save(user);
                }
                return {
                    status: 'success',
                    message: 'User already registered',
                    user,
                    isExisting: true
                };
            }
            console.log('Creating new user:', email);
            user = this.userRepository.create({
                email,
                fullName,
                clerkId
            });
            const savedUser = await this.userRepository.save(user);
            console.log('New user created:', savedUser);
            return {
                status: 'success',
                message: 'User created successfully',
                user: savedUser,
                isExisting: false
            };
        }
        catch (error) {
            console.error('Error in createUser:', error);
            if (error?.code === '23505') {
                throw new common_1.BadRequestException('Email already registered');
            }
            throw new common_1.BadRequestException('Failed to process user registration: ' + error.message);
        }
    }
    async findUserByClerkId(clerkId) {
        if (!clerkId) {
            return null;
        }
        return this.userRepository.findOne({ where: { clerkId } });
    }
    async updateLocation(email, latitude, longitude) {
        try {
            const user = await this.userRepository.findOne({ where: { email } });
            if (!user) {
                throw new Error('User not found');
            }
            user.latitude = latitude;
            user.longitude = longitude;
            user.lastLocationUpdate = new Date();
            await this.userRepository.save(user);
            return {
                success: true,
                message: 'Location updated successfully'
            };
        }
        catch (error) {
            throw new Error(`Failed to update location: ${error.message}`);
        }
    }
    async updateVIPStatus(email, isVIP) {
        try {
            const user = await this.userRepository.findOne({ where: { email } });
            if (!user) {
                throw new Error('User not found');
            }
            user.isVIP = isVIP;
            await this.userRepository.save(user);
            return {
                success: true,
                message: `VIP status ${isVIP ? 'enabled' : 'disabled'} successfully`,
                isVIP
            };
        }
        catch (error) {
            throw new Error(`Failed to update VIP status: ${error.message}`);
        }
    }
    async getUserProfile(email) {
        try {
            const user = await this.userRepository.findOne({ where: { email } });
            if (!user) {
                throw new Error('User not found');
            }
            return {
                success: true,
                user: {
                    id: user.id,
                    email: user.email,
                    fullName: user.fullName,
                    mobileNumber: user.mobileNumber,
                    isVIP: user.isVIP,
                    lastLocationUpdate: user.lastLocationUpdate
                }
            };
        }
        catch (error) {
            throw new Error(`Failed to get user profile: ${error.message}`);
        }
    }
    async updateUserDetails(email, fullName, mobileNumber) {
        try {
            const user = await this.userRepository.findOne({ where: { email } });
            if (!user) {
                throw new common_1.NotFoundException('User not found');
            }
            if (fullName !== undefined) {
                user.fullName = fullName;
            }
            if (mobileNumber !== undefined) {
                if (mobileNumber && !/^\+?[0-9]{10,15}$/.test(mobileNumber)) {
                    throw new common_1.BadRequestException('Invalid mobile number format');
                }
                user.mobileNumber = mobileNumber;
            }
            await this.userRepository.save(user);
            return {
                success: true,
                message: 'User details updated successfully',
                user: {
                    id: user.id,
                    email: user.email,
                    fullName: user.fullName,
                    mobileNumber: user.mobileNumber,
                    isVIP: user.isVIP
                }
            };
        }
        catch (error) {
            if (error instanceof common_1.BadRequestException) {
                throw error;
            }
            throw new Error(`Failed to update user details: ${error.message}`);
        }
    }
    async updateClerkId(email, clerkId) {
        try {
            if (!email || !clerkId) {
                throw new common_1.BadRequestException('Email and clerk ID are required');
            }
            const user = await this.userRepository.findOne({ where: { email } });
            if (!user) {
                throw new common_1.NotFoundException('User not found');
            }
            user.clerkId = clerkId;
            await this.userRepository.save(user);
            return {
                success: true,
                message: 'User clerk ID updated successfully',
                user: {
                    id: user.id,
                    email: user.email,
                    clerkId: user.clerkId
                }
            };
        }
        catch (error) {
            if (error instanceof common_1.BadRequestException || error instanceof common_1.NotFoundException) {
                throw error;
            }
            throw new Error(`Failed to update user clerk ID: ${error.message}`);
        }
    }
    async addToWishlist(email, serviceId) {
        try {
            const user = await this.userRepository.findOne({ where: { email } });
            if (!user) {
                throw new common_1.NotFoundException('User not found');
            }
            if (!user.wishlist) {
                user.wishlist = [];
            }
            if (!user.wishlist.includes(serviceId)) {
                user.wishlist.push(serviceId);
                await this.userRepository.save(user);
                return {
                    success: true,
                    message: 'Service added to wishlist',
                    wishlist: user.wishlist
                };
            }
            else {
                return {
                    success: true,
                    message: 'Service already in wishlist',
                    wishlist: user.wishlist
                };
            }
        }
        catch (error) {
            throw new Error(`Failed to add service to wishlist: ${error.message}`);
        }
    }
    async removeFromWishlist(email, serviceId) {
        try {
            const user = await this.userRepository.findOne({ where: { email } });
            if (!user) {
                throw new common_1.NotFoundException('User not found');
            }
            if (user.wishlist && user.wishlist.includes(serviceId)) {
                user.wishlist = user.wishlist.filter(id => id !== serviceId);
                await this.userRepository.save(user);
                return {
                    success: true,
                    message: 'Service removed from wishlist',
                    wishlist: user.wishlist
                };
            }
            else {
                return {
                    success: false,
                    message: 'Service not found in wishlist',
                    wishlist: user.wishlist || []
                };
            }
        }
        catch (error) {
            throw new Error(`Failed to remove service from wishlist: ${error.message}`);
        }
    }
    async getWishlist(email) {
        try {
            const user = await this.userRepository.findOne({ where: { email } });
            if (!user) {
                throw new common_1.NotFoundException('User not found');
            }
            const wishlistItems = (user.wishlist || []).map(serviceId => ({
                serviceId
            }));
            return {
                status: "success",
                wishlist: wishlistItems
            };
        }
        catch (error) {
            throw new Error(`Failed to get wishlist: ${error.message}`);
        }
    }
    async joinQueue(joinQueueDto) {
        const { serviceId, userId, userName, mobileNumber, date, timeSlot, amount, isVIP: requestIsVIP, hasSubUnits, subUnitId, subUnitName } = joinQueueDto;
        const service = await this.serviceRepository.findOne({
            where: { id: Number(serviceId) },
        });
        if (!service) {
            throw new common_1.NotFoundException(`Service with ID ${serviceId} not found`);
        }
        let dbUserId = null;
        let isVIP = requestIsVIP;
        try {
            const userByClerkId = await this.findUserByClerkId(userId);
            if (userByClerkId) {
                dbUserId = userByClerkId.id;
                isVIP = requestIsVIP ?? userByClerkId.isVIP;
            }
            else if (userId.includes('@')) {
                const userByEmail = await this.userRepository.findOne({ where: { email: userId } });
                if (userByEmail) {
                    dbUserId = userByEmail.id;
                    isVIP = requestIsVIP ?? userByEmail.isVIP;
                }
            }
        }
        catch (error) {
            console.error('Error finding user for queue:', error);
        }
        const existingQueue = await this.queueRepository.findOne({
            where: {
                userId,
                serviceId: Number(serviceId),
                date: new Date(date),
                timeSlot,
                status: 'waiting',
            },
        });
        if (existingQueue) {
            throw new common_1.BadRequestException('You already have a queue entry for this service, date and time slot');
        }
        const uniqueSlotId = Math.floor(1000 + Math.random() * 9000).toString();
        const serviceSetup = await this.serviceSetupRepository.findOne({
            where: { service: { id: Number(serviceId) } }
        });
        const hasSubUnitsFlag = serviceSetup?.setupData?.hasSubUnits || false;
        const allQueues = await this.queueRepository.find({
            where: {
                serviceId: Number(serviceId),
                date: new Date(date),
                timeSlot,
                status: (0, typeorm_2.In)(['waiting', 'serving']),
            },
            order: {
                isVIP: 'DESC',
                position: 'ASC',
                createdAt: 'ASC'
            }
        });
        const servingQueues = allQueues.filter(q => q.status === 'serving');
        const waitingQueues = allQueues.filter(q => q.status === 'waiting');
        console.log(`Found ${servingQueues.length} serving queues and ${waitingQueues.length} waiting queues for service ${serviceId}, date ${date}, timeSlot ${timeSlot}`);
        let position = 1;
        const vipCount = allQueues.filter(q => q.isVIP).length;
        const normalCount = allQueues.filter(q => !q.isVIP).length;
        const totalCount = vipCount + normalCount;
        console.log(`Queue counts - VIP: ${vipCount}, Normal: ${normalCount}, Total: ${totalCount}`);
        if (isVIP) {
            const firstPersonInQueue = allQueues.find(q => q.position === 1);
            if (!firstPersonInQueue) {
                position = 1;
                console.log(`VIP user will be in position ${position} (first in empty queue)`);
            }
            else if (firstPersonInQueue.status === 'waiting') {
                position = 1;
                console.log(`VIP user will be in position ${position} (jumping ahead of waiting first person)`);
                for (const q of allQueues) {
                    q.position += 1;
                }
                if (allQueues.length > 0) {
                    await this.queueRepository.save(allQueues);
                    for (const q of allQueues) {
                        const existingRedisData = await this.redisService.getQueue(q.id.toString());
                        if (existingRedisData) {
                            existingRedisData.position = q.position;
                            await this.redisService.saveQueue(q.id.toString(), existingRedisData);
                            console.log(`Updated Redis position for queue ${q.id} to position ${q.position}`);
                        }
                    }
                }
            }
            else if (firstPersonInQueue.status === 'serving') {
                const vipQueuesWaiting = waitingQueues.filter(q => q.isVIP);
                if (vipQueuesWaiting.length === 0) {
                    position = 2;
                    console.log(`VIP user will be in position ${position} (first person serving, no VIPs waiting)`);
                    const nonVipWaitingQueues = waitingQueues.filter(q => !q.isVIP);
                    for (const q of nonVipWaitingQueues) {
                        q.position += 1;
                    }
                    if (nonVipWaitingQueues.length > 0) {
                        await this.queueRepository.save(nonVipWaitingQueues);
                        for (const q of nonVipWaitingQueues) {
                            const existingRedisData = await this.redisService.getQueue(q.id.toString());
                            if (existingRedisData) {
                                existingRedisData.position = q.position;
                                await this.redisService.saveQueue(q.id.toString(), existingRedisData);
                                console.log(`Updated Redis position for queue ${q.id} to position ${q.position}`);
                            }
                        }
                    }
                }
                else {
                    const lastVipPosition = Math.max(...vipQueuesWaiting.map(q => q.position));
                    position = lastVipPosition + 1;
                    console.log(`VIP user will be in position ${position} (after last VIP at position ${lastVipPosition})`);
                    const nonVipQueuesToUpdate = waitingQueues.filter(q => !q.isVIP && q.position >= position);
                    for (const q of nonVipQueuesToUpdate) {
                        q.position += 1;
                    }
                    if (nonVipQueuesToUpdate.length > 0) {
                        await this.queueRepository.save(nonVipQueuesToUpdate);
                        for (const q of nonVipQueuesToUpdate) {
                            const existingRedisData = await this.redisService.getQueue(q.id.toString());
                            if (existingRedisData) {
                                existingRedisData.position = q.position;
                                await this.redisService.saveQueue(q.id.toString(), existingRedisData);
                                console.log(`Updated Redis position for queue ${q.id} to position ${q.position}`);
                            }
                        }
                    }
                }
            }
            else {
                position = vipCount + 1;
                console.log(`VIP user will be in position ${position} (fallback logic)`);
                const nonVipWaitingQueues = waitingQueues.filter(q => !q.isVIP);
                for (const q of nonVipWaitingQueues) {
                    q.position += 1;
                }
                if (nonVipWaitingQueues.length > 0) {
                    await this.queueRepository.save(nonVipWaitingQueues);
                    for (const q of nonVipWaitingQueues) {
                        const existingRedisData = await this.redisService.getQueue(q.id.toString());
                        if (existingRedisData) {
                            existingRedisData.position = q.position;
                            await this.redisService.saveQueue(q.id.toString(), existingRedisData);
                            console.log(`Updated Redis position for queue ${q.id} to position ${q.position}`);
                        }
                    }
                }
            }
        }
        else {
            position = totalCount + 1;
            console.log(`Normal user will be in position ${position} (after all VIPs and normals)`);
        }
        console.log(`Calculated position ${position} for new queue (isVIP: ${isVIP})`);
        if (servingQueues.length > 0) {
            console.log(`There are ${servingQueues.length} queues currently being served, included in position calculation`);
        }
        console.log(`Setting position ${position} for new queue in service ${serviceId}, date ${date}, timeSlot ${timeSlot}, isVIP: ${isVIP}`);
        const newQueue = new queue_entity_1.Queue();
        newQueue.serviceId = Number(serviceId);
        newQueue.userId = userId;
        newQueue.userName = userName;
        newQueue.date = new Date(date);
        newQueue.timeSlot = timeSlot;
        newQueue.status = 'waiting';
        newQueue.isVIP = isVIP || false;
        newQueue.uniqueSlotId = uniqueSlotId;
        newQueue.hasSubUnits = hasSubUnitsFlag || false;
        newQueue.subUnitId = subUnitId || '';
        newQueue.subUnitName = subUnitName || '';
        newQueue.position = position;
        newQueue.initialPositionAtJoin = position;
        newQueue.createdAt = new Date();
        if (hasSubUnitsFlag && subUnitId && serviceSetup?.setupData?.subUnits) {
            const subUnitIndex = parseInt(subUnitId, 10);
            const subUnit = serviceSetup.setupData.subUnits[subUnitIndex];
            if (subUnit?.avgServeTime) {
                newQueue.serveTime = parseInt(subUnit.avgServeTime, 10);
                console.log(`Setting serve time from subunit ${subUnitId} (${subUnit.name}): ${newQueue.serveTime} minutes`);
            }
            else {
                newQueue.serveTime = 15;
                console.log(`No serve time found for subunit ${subUnitId}, using default: 15 minutes`);
            }
        }
        else if (serviceSetup?.setupData?.servingTime) {
            newQueue.serveTime = parseInt(serviceSetup.setupData.servingTime, 10);
            console.log(`Setting serve time from service: ${newQueue.serveTime} minutes`);
        }
        else {
            newQueue.serveTime = 15;
            console.log(`No serve time found for service, using default: 15 minutes`);
        }
        const savedQueue = await this.queueRepository.save(newQueue);
        console.log(`New queue created with ID ${savedQueue.id} for service ${serviceId}`);
        try {
            const queueFlowService = await this.getQueueFlowService();
            if (queueFlowService) {
                console.log(`Calculating estimated serve time for new queue ${savedQueue.id}`);
                await queueFlowService.calculateEstimatedServeTime(savedQueue.id);
                await queueFlowService.recalculateEstimatedServeTimes(savedQueue.serviceId, savedQueue.date, savedQueue.timeSlot);
            }
            else {
                console.error(`QueueFlowService not available for calculating estimated serve time`);
            }
        }
        catch (error) {
            console.error(`Error calculating estimated serve time for queue ${savedQueue.id}:`, error);
        }
        const dateStr = (() => {
            if (!date) {
                return new Date().toISOString().split('T')[0];
            }
            return new Date(date).toISOString().split('T')[0];
        })();
        const queueDataForRedis = {
            ...savedQueue,
            amount,
            mobileNumber,
            serviceName: service.serviceName,
            serviceType: service.serviceType,
            uniqueSlotId,
            hasSubUnits: hasSubUnitsFlag,
            subUnitId: subUnitId || null,
            subUnitName: subUnitName || null,
            position,
            initialPositionAtJoin: position,
            serveTime: savedQueue.serveTime
        };
        if (dbUserId !== null) {
            Object.assign(queueDataForRedis, { dbUserId });
        }
        await this.redisService.saveQueue(savedQueue.id.toString(), queueDataForRedis);
        const countKey = isVIP
            ? `queue:vipcount:${serviceId}:${dateStr}:${timeSlot}`
            : `queue:normalcount:${serviceId}:${dateStr}:${timeSlot}`;
        const currentCount = await this.redisService.get(countKey) || 0;
        await this.redisService.set(countKey, parseInt(currentCount.toString()) + 1);
        const positionsKey = `service:${serviceId}:positions:${dateStr}:${timeSlot}`;
        const positionsData = await this.redisService.get(positionsKey) || {};
        positionsData[savedQueue.id] = position;
        await this.redisService.set(positionsKey, positionsData);
        await this.redisService.saveQueuePosition(serviceId.toString(), dateStr, timeSlot, positionsData);
        const activeQueuesKey = `service:${serviceId}:active-queues:${dateStr}`;
        const allActiveQueuesKey = `service:${serviceId}:active-queues:all`;
        await this.redisService.del(activeQueuesKey);
        await this.redisService.del(allActiveQueuesKey);
        await this.redisService.invalidateServiceQueues(Number(serviceId), dateStr);
        await this.redisService.invalidateServiceQueues(Number(serviceId), 'all');
        const queueCountsKey = `service:${serviceId}:queue-counts:${dateStr}`;
        await this.redisService.del(queueCountsKey);
        const positionKey = `service:${serviceId}:position:${dateStr}:${timeSlot}`;
        await this.redisService.del(positionKey);
        console.log(`Invalidated Redis caches for fast refresh after queue creation`);
        return savedQueue;
    }
    async calculatePositionForReschedule(serviceId, date, timeSlot, isVIP, excludeQueueId) {
        const allQueues = await this.queueRepository.find({
            where: {
                serviceId,
                date: new Date(date),
                timeSlot,
                status: (0, typeorm_2.In)(['waiting', 'checked-in', 'serving']),
            },
            order: {
                isVIP: 'DESC',
                createdAt: 'ASC'
            }
        });
        const filteredQueues = allQueues.filter(q => q.id !== excludeQueueId);
        console.log(`Found ${filteredQueues.length} existing queues for reschedule position calculation (excluding queue ${excludeQueueId})`);
        let position = 1;
        if (filteredQueues.length === 0) {
            position = 1;
            console.log(`Rescheduled queue will be in position ${position} (first in empty slot)`);
        }
        else {
            const vipCount = filteredQueues.filter(q => q.isVIP).length;
            const normalCount = filteredQueues.filter(q => !q.isVIP).length;
            const totalCount = vipCount + normalCount;
            console.log(`Queue counts for reschedule - VIP: ${vipCount}, Normal: ${normalCount}, Total: ${totalCount}`);
            if (isVIP) {
                const firstPersonInQueue = filteredQueues.find(q => q.position === 1);
                if (!firstPersonInQueue) {
                    position = 1;
                    console.log(`VIP rescheduled queue will be in position ${position} (first in empty queue)`);
                }
                else if (firstPersonInQueue.status === 'waiting') {
                    position = 1;
                    console.log(`VIP rescheduled queue will be in position ${position} (jumping ahead of waiting first person)`);
                    for (const q of filteredQueues) {
                        q.position += 1;
                    }
                    if (filteredQueues.length > 0) {
                        await this.queueRepository.save(filteredQueues);
                        for (const q of filteredQueues) {
                            const existingRedisData = await this.redisService.getQueue(q.id.toString());
                            if (existingRedisData) {
                                existingRedisData.position = q.position;
                                await this.redisService.saveQueue(q.id.toString(), existingRedisData);
                                console.log(`Updated Redis position for queue ${q.id} to position ${q.position} due to VIP reschedule`);
                            }
                        }
                    }
                }
                else if (firstPersonInQueue.status === 'serving') {
                    const vipQueuesWaiting = filteredQueues.filter(q => q.isVIP && q.status === 'waiting');
                    if (vipQueuesWaiting.length === 0) {
                        position = 2;
                        console.log(`VIP rescheduled queue will be in position ${position} (first person serving, no VIPs waiting)`);
                        const nonVipWaitingQueues = filteredQueues.filter(q => !q.isVIP && q.status === 'waiting');
                        for (const q of nonVipWaitingQueues) {
                            q.position += 1;
                        }
                        if (nonVipWaitingQueues.length > 0) {
                            await this.queueRepository.save(nonVipWaitingQueues);
                            for (const q of nonVipWaitingQueues) {
                                const existingRedisData = await this.redisService.getQueue(q.id.toString());
                                if (existingRedisData) {
                                    existingRedisData.position = q.position;
                                    await this.redisService.saveQueue(q.id.toString(), existingRedisData);
                                    console.log(`Updated Redis position for queue ${q.id} to position ${q.position} due to VIP reschedule`);
                                }
                            }
                        }
                    }
                    else {
                        const lastVipPosition = Math.max(...vipQueuesWaiting.map(q => q.position));
                        position = lastVipPosition + 1;
                        console.log(`VIP rescheduled queue will be in position ${position} (after last VIP at position ${lastVipPosition})`);
                        const nonVipQueuesToUpdate = filteredQueues.filter(q => !q.isVIP && q.status === 'waiting' && q.position >= position);
                        for (const q of nonVipQueuesToUpdate) {
                            q.position += 1;
                        }
                        if (nonVipQueuesToUpdate.length > 0) {
                            await this.queueRepository.save(nonVipQueuesToUpdate);
                            for (const q of nonVipQueuesToUpdate) {
                                const existingRedisData = await this.redisService.getQueue(q.id.toString());
                                if (existingRedisData) {
                                    existingRedisData.position = q.position;
                                    await this.redisService.saveQueue(q.id.toString(), existingRedisData);
                                    console.log(`Updated Redis position for queue ${q.id} to position ${q.position} due to VIP reschedule`);
                                }
                            }
                        }
                    }
                }
                else {
                    position = vipCount + 1;
                    console.log(`VIP rescheduled queue will be in position ${position} (fallback logic)`);
                    const nonVipWaitingQueues = filteredQueues.filter(q => !q.isVIP && q.status === 'waiting');
                    for (const q of nonVipWaitingQueues) {
                        q.position += 1;
                    }
                    if (nonVipWaitingQueues.length > 0) {
                        await this.queueRepository.save(nonVipWaitingQueues);
                        for (const q of nonVipWaitingQueues) {
                            const existingRedisData = await this.redisService.getQueue(q.id.toString());
                            if (existingRedisData) {
                                existingRedisData.position = q.position;
                                await this.redisService.saveQueue(q.id.toString(), existingRedisData);
                                console.log(`Updated Redis position for queue ${q.id} to position ${q.position} due to VIP reschedule`);
                            }
                        }
                    }
                }
            }
            else {
                position = totalCount + 1;
                console.log(`Normal rescheduled queue will be in position ${position} (after all VIPs and normals)`);
            }
        }
        return position;
    }
    async rescheduleQueue(rescheduleQueueDto) {
        const { oldQueueId, serviceId, userId, userName, mobileNumber, date, timeSlot, amount, isVIP: requestIsVIP, hasSubUnits, subUnitId, subUnitName } = rescheduleQueueDto;
        console.log(`Starting reschedule process for queue ${oldQueueId} to new date ${date} and time ${timeSlot}`);
        const oldQueue = await this.queueRepository.findOne({
            where: { id: Number(oldQueueId), userId },
            relations: ['service'],
        });
        if (!oldQueue) {
            throw new common_1.NotFoundException(`Queue with ID ${oldQueueId} not found for this user`);
        }
        if (oldQueue.status === 'completed' || oldQueue.status === 'cancelled') {
            throw new common_1.BadRequestException(`Cannot reschedule a ${oldQueue.status} queue`);
        }
        const service = await this.serviceRepository.findOne({
            where: { id: Number(serviceId) },
        });
        if (!service) {
            throw new common_1.NotFoundException(`Service with ID ${serviceId} not found`);
        }
        let dbUserId = null;
        let isVIP = requestIsVIP;
        try {
            const userByClerkId = await this.findUserByClerkId(userId);
            if (userByClerkId) {
                dbUserId = userByClerkId.id;
                isVIP = requestIsVIP ?? userByClerkId.isVIP;
            }
            else if (userId.includes('@')) {
                const userByEmail = await this.userRepository.findOne({ where: { email: userId } });
                if (userByEmail) {
                    dbUserId = userByEmail.id;
                    isVIP = requestIsVIP ?? userByEmail.isVIP;
                }
            }
        }
        catch (error) {
            console.error('Error finding user for reschedule:', error);
        }
        const existingQueue = await this.queueRepository.findOne({
            where: {
                userId,
                serviceId: Number(serviceId),
                date: new Date(date),
                timeSlot,
                status: 'waiting',
            },
        });
        if (existingQueue && existingQueue.id !== oldQueue.id) {
            throw new common_1.BadRequestException('You already have a queue entry for this service, date and time slot');
        }
        const oldDateStr = oldQueue.date instanceof Date
            ? oldQueue.date.toISOString().split('T')[0]
            : new Date(oldQueue.date).toISOString().split('T')[0];
        const newDateStr = new Date(date).toISOString().split('T')[0];
        const isChangingTimeslot = (oldDateStr !== newDateStr) || (oldQueue.timeSlot !== timeSlot);
        if (isChangingTimeslot && oldQueue.status === 'waiting') {
            console.log(`Reordering old timeslot queue positions after queue ${oldQueue.id} leaves`);
            const allQueuesInOldSlot = await this.queueRepository.find({
                where: {
                    serviceId: oldQueue.serviceId,
                    date: oldQueue.date,
                    timeSlot: oldQueue.timeSlot,
                    status: (0, typeorm_2.In)(['waiting', 'checked-in', 'serving']),
                },
                order: {
                    position: 'ASC'
                }
            });
            const servingPerson = allQueuesInOldSlot.find(q => q.status === 'serving' && q.position === 1);
            const remainingQueues = allQueuesInOldSlot.filter(q => q.id !== oldQueue.id &&
                (q.status === 'waiting' || q.status === 'checked-in'));
            if (remainingQueues.length > 0) {
                console.log(`Found ${remainingQueues.length} remaining queues to reorder in old timeslot`);
                console.log(`Serving person at position 1: ${servingPerson ? 'Yes' : 'No'}`);
                const vipQueues = remainingQueues.filter(q => q.isVIP);
                const normalQueues = remainingQueues.filter(q => !q.isVIP);
                let position = servingPerson ? 1 : 0;
                const positions = {};
                if (servingPerson) {
                    positions[servingPerson.id] = 1;
                }
                for (const q of vipQueues) {
                    position++;
                    q.position = position;
                    positions[q.id] = position;
                    console.log(`Reordered VIP queue ${q.id} to position ${position}`);
                }
                for (const q of normalQueues) {
                    position++;
                    q.position = position;
                    positions[q.id] = position;
                    console.log(`Reordered normal queue ${q.id} to position ${position}`);
                }
                await this.queueRepository.save(remainingQueues);
                for (const q of remainingQueues) {
                    const existingRedisData = await this.redisService.getQueue(q.id.toString());
                    if (existingRedisData) {
                        existingRedisData.position = q.position;
                        await this.redisService.saveQueue(q.id.toString(), existingRedisData);
                        console.log(`Updated Redis position for queue ${q.id} to position ${q.position} after reschedule departure`);
                    }
                }
                await this.redisService.saveQueuePosition(oldQueue.serviceId.toString(), oldDateStr, oldQueue.timeSlot, positions);
                console.log(`Reordered ${remainingQueues.length} queues in old timeslot after departure`);
            }
        }
        const position = await this.calculatePositionForReschedule(Number(serviceId), date, timeSlot, isVIP || false, oldQueue.id);
        console.log(`Calculated new position ${position} for rescheduled queue`);
        oldQueue.date = new Date(date);
        oldQueue.timeSlot = timeSlot;
        oldQueue.position = position;
        oldQueue.initialPositionAtJoin = position;
        oldQueue.status = 'waiting';
        oldQueue.hasSubUnits = hasSubUnits || false;
        oldQueue.subUnitId = subUnitId || '';
        oldQueue.subUnitName = subUnitName || '';
        if (isChangingTimeslot) {
            oldQueue.isCheckedIn = false;
            console.log(`Reset check-in status to false for rescheduled queue ${oldQueue.id}`);
        }
        const serviceSetup = await this.serviceSetupRepository.findOne({
            where: { service: { id: Number(serviceId) } },
            relations: ['service'],
        });
        if (hasSubUnits && subUnitId && serviceSetup?.setupData?.subUnits) {
            const subUnit = serviceSetup.setupData.subUnits.find((unit) => unit.name === subUnitName);
            if (subUnit?.avgServeTime) {
                oldQueue.serveTime = parseInt(subUnit.avgServeTime, 10);
                console.log(`Setting serve time from subunit: ${oldQueue.serveTime} minutes`);
            }
        }
        else if (serviceSetup?.setupData?.servingTime) {
            oldQueue.serveTime = parseInt(serviceSetup.setupData.servingTime, 10);
            console.log(`Setting serve time from service: ${oldQueue.serveTime} minutes`);
        }
        else {
            oldQueue.serveTime = 15;
            console.log(`No serve time found for service, using default: 15 minutes`);
        }
        const savedQueue = await this.queueRepository.save(oldQueue);
        console.log(`Queue ${savedQueue.id} rescheduled successfully`);
        try {
            const queueFlowService = await this.getQueueFlowService();
            if (queueFlowService) {
                console.log(`Calculating estimated serve time for rescheduled queue ${savedQueue.id}`);
                await queueFlowService.calculateEstimatedServeTime(savedQueue.id);
                await queueFlowService.recalculateEstimatedServeTimes(savedQueue.serviceId, savedQueue.date, savedQueue.timeSlot);
            }
        }
        catch (error) {
            console.error(`Error calculating estimated serve time for rescheduled queue ${savedQueue.id}:`, error);
        }
        const dateStr = new Date(date).toISOString().split('T')[0];
        const queueDataForRedis = {
            ...savedQueue,
            amount,
            mobileNumber,
            serviceName: service.serviceName,
            serviceType: service.serviceType,
            uniqueSlotId: oldQueue.uniqueSlotId || Math.floor(1000 + Math.random() * 9000).toString(),
            hasSubUnits: hasSubUnits || false,
            subUnitId: subUnitId || null,
            subUnitName: subUnitName || null,
            position,
            initialPositionAtJoin: position,
            serveTime: savedQueue.serveTime,
            isCheckedIn: savedQueue.isCheckedIn
        };
        if (dbUserId !== null) {
            Object.assign(queueDataForRedis, { dbUserId });
        }
        await this.redisService.saveQueue(savedQueue.id.toString(), queueDataForRedis);
        const newPositionsKey = `service:${serviceId}:positions:${dateStr}:${timeSlot}`;
        const newPositionsData = await this.redisService.get(newPositionsKey) || {};
        newPositionsData[savedQueue.id] = position;
        await this.redisService.set(newPositionsKey, newPositionsData);
        await this.redisService.saveQueuePosition(serviceId.toString(), dateStr, timeSlot, newPositionsData);
        const cacheKeysToInvalidate = [
            `service:${serviceId}:active-queues:${oldDateStr}`,
            `service:${serviceId}:active-queues:${dateStr}`,
            `service:${serviceId}:active-queues:all`,
            `service:${serviceId}:queue-counts:${oldDateStr}`,
            `service:${serviceId}:queue-counts:${dateStr}`,
            `service:${serviceId}:position:${oldDateStr}:${oldQueue.timeSlot}`,
            `service:${serviceId}:position:${dateStr}:${timeSlot}`
        ];
        for (const key of cacheKeysToInvalidate) {
            await this.redisService.del(key);
        }
        await this.redisService.invalidateServiceQueues(Number(serviceId), oldDateStr);
        await this.redisService.invalidateServiceQueues(Number(serviceId), dateStr);
        await this.redisService.invalidateServiceQueues(Number(serviceId), 'all');
        console.log(`Invalidated Redis caches for reschedule operation`);
        return savedQueue;
    }
    async getUserQueues(userId) {
        const queues = await this.queueRepository.find({
            where: { userId },
            relations: ['service'],
            order: { createdAt: 'DESC' },
        });
        const upcoming = [];
        const completed = [];
        const cancelled = [];
        const noShow = [];
        const today = new Date();
        const todayStr = today.toISOString().split('T')[0];
        for (const queue of queues) {
            const redisKey = `queue:${queue.id}`;
            const redisData = await this.redisService.get(redisKey);
            let uniqueSlotId = queue.uniqueSlotId;
            let hasSubUnits = queue.hasSubUnits || false;
            let subUnitId = queue.subUnitId || '';
            let subUnitName = queue.subUnitName || '';
            if (redisData && typeof redisData === 'object') {
                if ('uniqueSlotId' in redisData) {
                    uniqueSlotId = redisData.uniqueSlotId;
                }
                if ('hasSubUnits' in redisData) {
                    hasSubUnits = redisData.hasSubUnits;
                }
                if ('subUnitId' in redisData) {
                    subUnitId = redisData.subUnitId;
                }
                if ('subUnitName' in redisData) {
                    subUnitName = redisData.subUnitName;
                }
            }
            if (!uniqueSlotId) {
                uniqueSlotId = Math.floor(1000 + Math.random() * 9000).toString();
            }
            if (hasSubUnits || subUnitId || subUnitName) {
                console.log(`Queue ${queue.id} has subunit: ${subUnitName} (ID: ${subUnitId}, hasSubUnits: ${hasSubUnits})`);
            }
            const queueDate = queue.date instanceof Date
                ? queue.date.toISOString()
                : typeof queue.date === 'string'
                    ? queue.date
                    : new Date().toISOString();
            const queueDateStr = queueDate.split('T')[0];
            let timeEnd = '';
            if (queue.timeSlot && queue.timeSlot.includes(' - ')) {
                timeEnd = queue.timeSlot.split(' - ')[1];
            }
            else if (queue.timeSlot) {
                timeEnd = queue.timeSlot;
            }
            else {
                timeEnd = '23:59';
            }
            let actualStatus = queue.status;
            if (queue.status === 'waiting' && this.hasTimeSlotEnded(queue.date, queue.timeSlot)) {
                actualStatus = queue.isCheckedIn ? 'completed' : 'no-show';
                queue.status = actualStatus;
                await this.queueRepository.save(queue);
                if (redisData) {
                    await this.redisService.updateQueueStatus(queue.id.toString(), actualStatus);
                }
            }
            const queueData = {
                id: queue.id,
                serviceId: queue.serviceId,
                serviceName: queue.service?.serviceName || 'Unknown Service',
                serviceType: queue.service?.serviceType || 'Unknown Type',
                date: queue.date,
                timeSlot: queue.timeSlot,
                status: actualStatus,
                isVIP: queue.isVIP,
                createdAt: queue.createdAt,
                uniqueSlotId,
                hasSubUnits,
                subUnitId,
                subUnitName
            };
            if (actualStatus === 'waiting' || actualStatus === 'checked-in' || actualStatus === 'serving') {
                upcoming.push(queueData);
            }
            else if (actualStatus === 'completed') {
                completed.push(queueData);
            }
            else if (actualStatus === 'cancelled') {
                cancelled.push(queueData);
            }
            else if (actualStatus === 'no-show') {
                noShow.push(queueData);
            }
        }
        return {
            upcoming,
            completed,
            cancelled,
            noShow
        };
    }
    async getQueuesProgressive(userId, section, offset = 0, limit = 1) {
        const statusMap = {
            'upcoming': ['waiting', 'checked-in', 'serving'],
            'completed': ['completed', 'no-show'],
            'cancelled': ['cancelled']
        };
        const statuses = statusMap[section];
        if (!statuses) {
            throw new common_1.BadRequestException('Invalid section parameter');
        }
        const queryBuilder = this.queueRepository
            .createQueryBuilder('queue')
            .leftJoinAndSelect('queue.service', 'service')
            .where('queue.userId = :userId', { userId })
            .andWhere('queue.status IN (:...statuses)', { statuses })
            .orderBy('queue.createdAt', section === 'upcoming' ? 'ASC' : 'DESC')
            .skip(offset)
            .limit(limit);
        const [queues, totalCount] = await queryBuilder.getManyAndCount();
        const processedQueues = [];
        for (const queue of queues) {
            const redisKey = `queue:${queue.id}`;
            const redisData = await this.redisService.get(redisKey);
            let uniqueSlotId = queue.uniqueSlotId;
            let hasSubUnits = queue.hasSubUnits || false;
            let subUnitId = queue.subUnitId || '';
            let subUnitName = queue.subUnitName || '';
            if (redisData && typeof redisData === 'object') {
                if ('uniqueSlotId' in redisData) {
                    uniqueSlotId = redisData.uniqueSlotId;
                }
                if ('hasSubUnits' in redisData) {
                    hasSubUnits = redisData.hasSubUnits;
                }
                if ('subUnitId' in redisData) {
                    subUnitId = redisData.subUnitId;
                }
                if ('subUnitName' in redisData) {
                    subUnitName = redisData.subUnitName;
                }
            }
            if (!uniqueSlotId) {
                uniqueSlotId = Math.floor(1000 + Math.random() * 9000).toString();
            }
            let actualStatus = queue.status;
            if (queue.status === 'waiting' && this.hasTimeSlotEnded(queue.date, queue.timeSlot)) {
                actualStatus = queue.isCheckedIn ? 'completed' : 'no-show';
                queue.status = actualStatus;
                await this.queueRepository.save(queue);
                if (redisData) {
                    await this.redisService.updateQueueStatus(queue.id.toString(), actualStatus);
                }
            }
            const queueData = {
                id: queue.id,
                serviceId: queue.serviceId,
                serviceName: queue.service?.serviceName || 'Unknown Service',
                serviceType: queue.service?.serviceType || 'Unknown Type',
                date: queue.date,
                timeSlot: queue.timeSlot,
                status: actualStatus,
                isVIP: queue.isVIP,
                createdAt: queue.createdAt,
                uniqueSlotId,
                hasSubUnits,
                subUnitId,
                subUnitName
            };
            processedQueues.push(queueData);
        }
        return {
            queues: processedQueues,
            pagination: {
                offset,
                limit,
                total: totalCount,
                hasMore: offset + limit < totalCount
            }
        };
    }
    async getQueuesByStatus(userId, status) {
        if (!['upcoming', 'completed', 'cancelled', 'no-show', 'serving'].includes(status)) {
            throw new common_1.BadRequestException('Invalid status parameter. Must be one of: upcoming, completed, cancelled, no-show, serving');
        }
        const statusMap = {
            'upcoming': 'waiting',
            'completed': 'completed',
            'cancelled': 'cancelled',
            'no-show': 'no-show',
            'serving': 'serving'
        };
        const today = new Date();
        const todayStr = today.toISOString().split('T')[0];
        let queues = await this.queueRepository.find({
            where: { userId, status: statusMap[status] },
            relations: ['service'],
            order: { createdAt: status === 'upcoming' ? 'ASC' : 'DESC' },
        });
        if (status === 'upcoming') {
            const filteredQueues = [];
            for (const queue of queues) {
                if (!this.hasTimeSlotEnded(queue.date, queue.timeSlot)) {
                    filteredQueues.push(queue);
                }
                else {
                    const newStatus = queue.isCheckedIn ? 'completed' : 'no-show';
                    queue.status = newStatus;
                    await this.queueRepository.save(queue);
                    const redisKey = `queue:${queue.id}`;
                    const redisData = await this.redisService.get(redisKey);
                    if (redisData) {
                        await this.redisService.updateQueueStatus(queue.id.toString(), newStatus);
                    }
                }
            }
            queues = filteredQueues;
        }
        if (status === 'completed') {
            const pastQueues = await this.queueRepository.find({
                where: { userId, status: 'waiting' },
                relations: ['service'],
            });
            const additionalCompletedQueues = [];
            for (const queue of pastQueues) {
                if (this.hasTimeSlotEnded(queue.date, queue.timeSlot) && queue.isCheckedIn) {
                    queue.status = 'completed';
                    await this.queueRepository.save(queue);
                    const redisKey = `queue:${queue.id}`;
                    const redisData = await this.redisService.get(redisKey);
                    if (redisData) {
                        await this.redisService.updateQueueStatus(queue.id.toString(), 'completed');
                    }
                    additionalCompletedQueues.push(queue);
                }
            }
            queues = [...queues, ...additionalCompletedQueues];
        }
        if (status === 'no-show') {
            const pastQueues = await this.queueRepository.find({
                where: { userId, status: 'waiting' },
                relations: ['service'],
            });
            const additionalNoShowQueues = [];
            for (const queue of pastQueues) {
                if (this.hasTimeSlotEnded(queue.date, queue.timeSlot) && !queue.isCheckedIn) {
                    queue.status = 'no-show';
                    await this.queueRepository.save(queue);
                    const redisKey = `queue:${queue.id}`;
                    const redisData = await this.redisService.get(redisKey);
                    if (redisData) {
                        await this.redisService.updateQueueStatus(queue.id.toString(), 'no-show');
                    }
                    additionalNoShowQueues.push(queue);
                }
            }
            queues = [...queues, ...additionalNoShowQueues];
        }
        return Promise.all(queues.map(async (queue) => {
            const redisKey = `queue:${queue.id}`;
            const redisData = await this.redisService.get(redisKey);
            let uniqueSlotId = queue.uniqueSlotId;
            let hasSubUnits = queue.hasSubUnits || false;
            let subUnitId = queue.subUnitId || '';
            let subUnitName = queue.subUnitName || '';
            if (redisData && typeof redisData === 'object') {
                if ('uniqueSlotId' in redisData) {
                    uniqueSlotId = redisData.uniqueSlotId;
                }
                if ('hasSubUnits' in redisData) {
                    hasSubUnits = redisData.hasSubUnits;
                }
                if ('subUnitId' in redisData) {
                    subUnitId = redisData.subUnitId;
                }
                if ('subUnitName' in redisData) {
                    subUnitName = redisData.subUnitName;
                }
            }
            if (!uniqueSlotId) {
                uniqueSlotId = Math.floor(1000 + Math.random() * 9000).toString();
            }
            if (hasSubUnits || subUnitId || subUnitName) {
                console.log(`Queue ${queue.id} has subunit: ${subUnitName} (ID: ${subUnitId}, hasSubUnits: ${hasSubUnits})`);
            }
            return {
                id: queue.id,
                serviceId: queue.serviceId,
                serviceName: queue.service?.serviceName || 'Unknown Service',
                serviceType: queue.service?.serviceType || 'Unknown Type',
                date: queue.date,
                timeSlot: queue.timeSlot,
                status: queue.status,
                isVIP: queue.isVIP,
                createdAt: queue.createdAt,
                uniqueSlotId,
                hasSubUnits,
                subUnitId,
                subUnitName
            };
        }));
    }
    async getActiveUserQueues(userId) {
        const queues = await this.queueRepository.find({
            where: { userId, status: 'waiting' },
            relations: ['service'],
            order: { createdAt: 'ASC' },
        });
        const today = new Date();
        const todayStr = today.toISOString().split('T')[0];
        const activeQueues = [];
        for (const queue of queues) {
            if (!this.hasTimeSlotEnded(queue.date, queue.timeSlot)) {
                activeQueues.push(queue);
            }
            else {
                queue.status = 'completed';
                await this.queueRepository.save(queue);
                const redisKey = `queue:${queue.id}`;
                const redisData = await this.redisService.get(redisKey);
                if (redisData) {
                    await this.redisService.updateQueueStatus(queue.id.toString(), 'completed');
                }
            }
        }
        const enhancedQueues = await Promise.all(activeQueues.map(async (queue) => {
            const redisKey = `queue:${queue.id}`;
            const redisData = await this.redisService.get(redisKey);
            if (redisData) {
                return redisData;
            }
            return {
                id: queue.id,
                serviceId: queue.serviceId,
                serviceName: queue.service.serviceName,
                serviceType: queue.service.serviceType,
                date: queue.date,
                timeSlot: queue.timeSlot,
                status: queue.status,
                isVIP: queue.isVIP,
                createdAt: queue.createdAt,
                uniqueSlotId: queue.uniqueSlotId || Math.floor(1000 + Math.random() * 9000).toString(),
                hasSubUnits: queue.hasSubUnits || false,
                subUnitId: queue.subUnitId || '',
                subUnitName: queue.subUnitName || ''
            };
        }));
        return enhancedQueues;
    }
    async getServiceActiveQueues(serviceId, date) {
        try {
            const redisKey = `service:${serviceId}:active-queues:${date}`;
            const cachedQueues = await this.redisService.get(redisKey);
            if (cachedQueues) {
                console.log(`Found ${cachedQueues.length} cached queues for service ${serviceId} on date ${date} in Redis`);
                const filteredQueues = [];
                const queueIdsToUpdate = [];
                for (const queue of cachedQueues) {
                    const isActiveStatus = queue.status === 'waiting' || queue.status === 'checked-in' || queue.status === 'serving';
                    if (!isActiveStatus) {
                        continue;
                    }
                    if (this.hasTimeSlotEnded(queue.date, queue.timeSlot)) {
                        queueIdsToUpdate.push(queue.id);
                    }
                    else {
                        filteredQueues.push(queue);
                    }
                }
                if (filteredQueues.length !== cachedQueues.length) {
                    await this.redisService.set(redisKey, filteredQueues, { ex: 120 });
                    console.log(`Filtered out ${cachedQueues.length - filteredQueues.length} ended time slots or non-active queues from cached data`);
                    if (queueIdsToUpdate.length > 0) {
                        this.updateQueueStatusByIds(queueIdsToUpdate, 'completed', serviceId, date);
                    }
                }
                return filteredQueues;
            }
            const whereClause = {
                serviceId,
                status: (0, typeorm_2.In)(['waiting', 'checked-in', 'serving'])
            };
            if (date !== 'all') {
                whereClause.date = new Date(date);
            }
            const queues = await this.queueRepository.find({
                where: whereClause,
                order: { createdAt: 'ASC' },
            });
            console.log(`Found ${queues.length} active queues for service ${serviceId} in database`);
            const filteredQueues = [];
            const queueIdsToUpdate = [];
            for (const queue of queues) {
                if (this.hasTimeSlotEnded(queue.date, queue.timeSlot)) {
                    queueIdsToUpdate.push(queue.id);
                }
                else {
                    filteredQueues.push(queue);
                }
            }
            if (queueIdsToUpdate.length > 0) {
                this.updateQueueStatusByIds(queueIdsToUpdate, 'completed', serviceId, date);
            }
            const activeQueues = filteredQueues.map(queue => ({
                id: queue.id,
                serviceId: queue.serviceId,
                timeSlot: queue.timeSlot,
                status: queue.status,
                isVIP: queue.isVIP,
                userId: queue.userId,
                date: queue.date,
                createdAt: queue.createdAt,
                hasSubUnits: queue.hasSubUnits || false,
                subUnitId: queue.subUnitId || '',
                subUnitName: queue.subUnitName || ''
            }));
            await this.redisService.set(redisKey, activeQueues, { ex: 120 });
            return activeQueues;
        }
        catch (error) {
            console.error(`Error fetching active queues for service ${serviceId} on date ${date}:`, error);
            throw new Error(`Failed to fetch active queues: ${error.message}`);
        }
    }
    async getQueueById(queueId, userId) {
        const queueIdNumber = typeof queueId === 'string' ? parseInt(queueId, 10) : queueId;
        let query = this.queueRepository
            .createQueryBuilder('queue')
            .leftJoinAndSelect('queue.service', 'service')
            .where('queue.id = :queueId', { queueId: queueIdNumber });
        if (userId) {
            query = query.andWhere('queue.userId = :userId', { userId });
        }
        const queue = await query.getOne();
        if (!queue) {
            return null;
        }
        const redisKey = `queue:${queueIdNumber}`;
        const redisData = await this.redisService.get(redisKey);
        if (redisData) {
            return {
                ...queue,
                ...redisData,
                id: queue.id,
                serviceId: queue.serviceId,
                service: queue.service
            };
        }
        return queue;
    }
    async checkInQueue(queueId, userId) {
        try {
            const queue = await this.queueRepository.findOne({
                where: { id: Number(queueId), userId },
                relations: ['service'],
            });
            if (!queue) {
                throw new common_1.NotFoundException(`Queue with ID ${queueId} not found for this user`);
            }
            queue.isCheckedIn = true;
            if (queue.status !== 'completed' && queue.status !== 'cancelled') {
                queue.status = 'waiting';
            }
            await this.queueRepository.save(queue);
            const queueData = await this.redisService.getQueue(queueId);
            if (queueData) {
                queueData.isCheckedIn = true;
                if (queueData.status !== 'completed' && queueData.status !== 'cancelled') {
                    queueData.status = 'waiting';
                }
                await this.redisService.saveQueue(queueId, queueData, 300);
            }
            if (queue.date) {
                const dateStr = (() => {
                    if (!queue.date) {
                        return new Date().toISOString().split('T')[0];
                    }
                    return new Date(queue.date).toISOString().split('T')[0];
                })();
                await this.redisService.invalidateServiceQueues(queue.serviceId, dateStr);
                await this.redisService.invalidateServiceQueues(queue.serviceId, 'all');
                const queueCountsKey = `service:${queue.serviceId}:queue-counts:${dateStr}`;
                await this.redisService.del(queueCountsKey);
                const activeQueuesKey = `service:${queue.serviceId}:active-queues:${dateStr}`;
                const allActiveQueuesKey = `service:${queue.serviceId}:active-queues:all`;
                await this.redisService.del(activeQueuesKey);
                await this.redisService.del(allActiveQueuesKey);
            }
            return {
                status: 'success',
                message: 'Successfully checked in',
                queue: {
                    id: queue.id,
                    status: queue.status,
                    isCheckedIn: queue.isCheckedIn
                }
            };
        }
        catch (error) {
            throw new Error(`Failed to check in: ${error.message}`);
        }
    }
    async toggleCheckInQueue(queueId, userId) {
        try {
            const queue = await this.queueRepository.findOne({
                where: { id: Number(queueId) },
                relations: ['service'],
            });
            if (!queue) {
                throw new Error('Queue not found');
            }
            if (queue.userId !== userId) {
                throw new Error('Unauthorized: This queue does not belong to you');
            }
            queue.isCheckedIn = !queue.isCheckedIn;
            const savedQueue = await this.queueRepository.save(queue);
            const date = queue.date instanceof Date
                ? queue.date.toISOString().split('T')[0]
                : queue.date;
            const queueData = await this.redisService.getQueue(queueId);
            if (queueData) {
                queueData.isCheckedIn = queue.isCheckedIn;
                await this.redisService.saveQueue(queueId, queueData, 300);
                await this.redisService.updateQueueCheckInStatus(queueId, queue.isCheckedIn);
            }
            return {
                status: 'success',
                message: queue.isCheckedIn ? 'Successfully checked in' : 'Check-in removed',
                data: {
                    queueId: savedQueue.id,
                    isCheckedIn: savedQueue.isCheckedIn,
                },
            };
        }
        catch (error) {
            throw new Error(error.message || 'Failed to toggle check-in status');
        }
    }
    async cancelQueue(queueId, userId) {
        try {
            const queue = await this.queueRepository.findOne({
                where: { id: Number(queueId), userId },
            });
            if (!queue) {
                throw new common_1.NotFoundException(`Queue with ID ${queueId} not found for this user`);
            }
            queue.status = 'cancelled';
            await this.queueRepository.save(queue);
            const queueData = await this.redisService.getQueue(queueId);
            if (queueData) {
                queueData.status = 'cancelled';
                await this.redisService.saveQueue(queueId, queueData, 300);
                await this.redisService.updateQueueStatus(queueId, 'cancelled');
            }
            if (queue.date) {
                const dateStr = (() => {
                    if (!queue.date) {
                        return new Date().toISOString().split('T')[0];
                    }
                    return new Date(queue.date).toISOString().split('T')[0];
                })();
                await this.redisService.invalidateServiceQueues(queue.serviceId, dateStr);
                await this.redisService.invalidateServiceQueues(queue.serviceId, 'all');
                const queueCountsKey = `service:${queue.serviceId}:queue-counts:${dateStr}`;
                await this.redisService.del(queueCountsKey);
                const activeQueuesKey = `service:${queue.serviceId}:active-queues:${dateStr}`;
                const allActiveQueuesKey = `service:${queue.serviceId}:active-queues:all`;
                await this.redisService.del(activeQueuesKey);
                await this.redisService.del(allActiveQueuesKey);
            }
            return {
                status: 'success',
                message: 'Successfully cancelled queue',
                queue: {
                    id: queue.id,
                    status: queue.status
                }
            };
        }
        catch (error) {
            throw new Error(`Failed to cancel queue: ${error.message}`);
        }
    }
    async getQueuePosition(queue) {
        try {
            const dateStr = (() => {
                if (!queue.date) {
                    return new Date().toISOString().split('T')[0];
                }
                return new Date(queue.date).toISOString().split('T')[0];
            })();
            const positionData = await this.redisService.getQueuePosition(queue.serviceId, dateStr, queue.timeSlot);
            if (positionData && positionData[queue.id]) {
                return positionData[queue.id];
            }
            const queuesForSlot = await this.queueRepository.find({
                where: {
                    serviceId: queue.serviceId,
                    date: queue.date,
                    timeSlot: queue.timeSlot,
                    status: (0, typeorm_2.In)(['waiting', 'checked-in']),
                },
                order: {
                    isVIP: 'DESC',
                    createdAt: 'ASC'
                }
            });
            const positions = {};
            let position = 0;
            for (const q of queuesForSlot) {
                position++;
                positions[q.id] = position;
                if (q.id === queue.id) {
                    break;
                }
            }
            await this.redisService.saveQueuePosition(queue.serviceId, dateStr, queue.timeSlot, positions);
            return positions[queue.id] || 0;
        }
        catch (error) {
            console.error('Error getting queue position:', error);
            return 0;
        }
    }
    async completeQueue(queueId, userId, isCheckedIn) {
        try {
            const queue = await this.queueRepository.findOne({
                where: { id: Number(queueId), userId },
            });
            if (!queue) {
                throw new common_1.NotFoundException(`Queue with ID ${queueId} not found for this user`);
            }
            const checkedIn = isCheckedIn !== undefined ? isCheckedIn : queue.isCheckedIn;
            const newStatus = checkedIn ? 'completed' : 'no-show';
            console.log(`Marking queue ${queueId} as ${newStatus} (isCheckedIn: ${checkedIn})`);
            queue.status = newStatus;
            await this.queueRepository.save(queue);
            const queueData = await this.redisService.getQueue(queueId);
            if (queueData) {
                queueData.status = newStatus;
                queueData.isCheckedIn = checkedIn;
                await this.redisService.saveQueue(queueId, queueData, 300);
                await this.redisService.updateQueueStatus(queueId, newStatus);
            }
            if (queue.date) {
                const dateStr = (() => {
                    if (!queue.date) {
                        return new Date().toISOString().split('T')[0];
                    }
                    return new Date(queue.date).toISOString().split('T')[0];
                })();
                await this.redisService.invalidateServiceQueues(queue.serviceId, dateStr);
                await this.redisService.invalidateServiceQueues(queue.serviceId, 'all');
                const queueCountsKey = `service:${queue.serviceId}:queue-counts:${dateStr}`;
                await this.redisService.del(queueCountsKey);
                const activeQueuesKey = `service:${queue.serviceId}:active-queues:${dateStr}`;
                const allActiveQueuesKey = `service:${queue.serviceId}:active-queues:all`;
                await this.redisService.del(activeQueuesKey);
                await this.redisService.del(allActiveQueuesKey);
            }
            return {
                status: 'success',
                message: 'Successfully completed queue',
                queue: {
                    id: queue.id,
                    status: queue.status
                }
            };
        }
        catch (error) {
            throw new Error(`Failed to complete queue: ${error.message}`);
        }
    }
    async getServiceQueueCounts(serviceId, date, subUnitId) {
        try {
            const redisKey = subUnitId
                ? `service:${serviceId}:queue-counts:${date}:subunit:${subUnitId}`
                : `service:${serviceId}:queue-counts:${date}`;
            const cachedCounts = await this.redisService.get(redisKey);
            if (cachedCounts) {
                console.log('Queue counts from Redis cache:', redisKey);
                return cachedCounts;
            }
            const activeQueues = await this.getServiceActiveQueues(serviceId, date);
            let filteredQueues = activeQueues;
            if (subUnitId !== undefined) {
                console.log(`Filtering queue counts by subUnitId: ${subUnitId}`);
                filteredQueues = activeQueues.filter(queue => queue.hasSubUnits && queue.subUnitId === subUnitId);
                console.log(`After filtering, found ${filteredQueues.length} queues for subUnitId ${subUnitId}`);
            }
            const timeSlotCounts = {};
            let needsQueueStatusUpdate = false;
            const queueIdsToUpdate = [];
            if (Array.isArray(filteredQueues)) {
                for (const queue of filteredQueues) {
                    if (!queue.timeSlot)
                        continue;
                    if (this.hasTimeSlotEnded(queue.date, queue.timeSlot)) {
                        needsQueueStatusUpdate = true;
                        queueIdsToUpdate.push(queue.id);
                        continue;
                    }
                    if (!timeSlotCounts[queue.timeSlot]) {
                        timeSlotCounts[queue.timeSlot] = {
                            normalCount: 0,
                            vipCount: 0
                        };
                    }
                    if (queue.isVIP) {
                        timeSlotCounts[queue.timeSlot].vipCount++;
                    }
                    else {
                        timeSlotCounts[queue.timeSlot].normalCount++;
                    }
                }
            }
            if (needsQueueStatusUpdate && queueIdsToUpdate.length > 0) {
                this.updateQueueStatusByIds(queueIdsToUpdate, 'completed', serviceId, date);
            }
            await this.redisService.set(redisKey, timeSlotCounts, { ex: 30 });
            return timeSlotCounts;
        }
        catch (error) {
            console.error(`Error getting queue counts for service ${serviceId} on date ${date}:`, error);
            return {};
        }
    }
    async getServiceQueuesByStatus(serviceId, date, status) {
        try {
            const redisKey = `service:${serviceId}:${status}-queues:${date}`;
            const cachedQueues = await this.redisService.get(redisKey);
            if (cachedQueues) {
                console.log(`Found ${cachedQueues.length} cached ${status} queues for service ${serviceId} in Redis`);
                return cachedQueues;
            }
            const whereClause = {
                serviceId,
                status
            };
            if (date !== 'all') {
                whereClause.date = new Date(date);
            }
            const queues = await this.queueRepository.find({
                where: whereClause,
                order: { createdAt: 'ASC' },
            });
            console.log(`Found ${queues.length} ${status} queues for service ${serviceId} in database`);
            const filteredQueues = queues.map(queue => ({
                id: queue.id,
                serviceId: queue.serviceId,
                timeSlot: queue.timeSlot,
                status: queue.status,
                isVIP: queue.isVIP,
                userId: queue.userId,
                date: queue.date,
                createdAt: queue.createdAt,
                position: 0,
                hasSubUnits: queue.hasSubUnits || false,
                subUnitId: queue.subUnitId || '',
                subUnitName: queue.subUnitName || ''
            }));
            await this.redisService.set(redisKey, filteredQueues, { ex: 120 });
            return filteredQueues;
        }
        catch (error) {
            console.error(`Error fetching ${status} queues for service ${serviceId} on date ${date}:`, error);
            throw new Error(`Failed to fetch ${status} queues: ${error.message}`);
        }
    }
    async updateExpiredQueues() {
        try {
            const waitingQueues = await this.queueRepository.find({
                where: { status: 'waiting' },
                relations: ['service']
            });
            console.log(`Found ${waitingQueues.length} waiting queues to check for expiration`);
            let updatedCount = {
                completed: 0,
                noShow: 0
            };
            for (const queue of waitingQueues) {
                if (this.hasTimeSlotEnded(queue.date, queue.timeSlot)) {
                    const newStatus = queue.isCheckedIn ? 'completed' : 'no-show';
                    queue.status = newStatus;
                    await this.queueRepository.save(queue);
                    if (newStatus === 'completed') {
                        updatedCount.completed++;
                    }
                    else {
                        updatedCount.noShow++;
                    }
                    const redisKey = `queue:${queue.id}`;
                    const redisData = await this.redisService.get(redisKey);
                    if (redisData) {
                        await this.redisService.updateQueueStatus(queue.id.toString(), newStatus);
                    }
                    const dateStr = (() => {
                        if (!queue.date) {
                            return new Date().toISOString().split('T')[0];
                        }
                        return new Date(queue.date).toISOString().split('T')[0];
                    })();
                    await this.redisService.invalidateServiceQueues(queue.serviceId, dateStr);
                    await this.redisService.invalidateServiceQueues(queue.serviceId, 'all');
                    const queueCountsKey = `service:${queue.serviceId}:queue-counts:${dateStr}`;
                    await this.redisService.del(queueCountsKey);
                    const activeQueuesKey = `service:${queue.serviceId}:active-queues:${dateStr}`;
                    const allActiveQueuesKey = `service:${queue.serviceId}:active-queues:all`;
                    await this.redisService.del(activeQueuesKey);
                    await this.redisService.del(allActiveQueuesKey);
                }
            }
            const totalUpdated = updatedCount.completed + updatedCount.noShow;
            return {
                updated: totalUpdated,
                message: `Updated ${totalUpdated} expired queues: ${updatedCount.completed} to 'completed' status and ${updatedCount.noShow} to 'no-show' status`
            };
        }
        catch (error) {
            console.error('Error updating expired queues:', error);
            return {
                updated: 0,
                message: `Failed to update expired queues: ${error.message}`
            };
        }
    }
    async updateQueueStatusByIds(queueIds, status, serviceId, date) {
        try {
            console.log(`Updating ${queueIds.length} queues to status '${status}' for service ${serviceId}`);
            await this.queueRepository.update({ id: (0, typeorm_2.In)(queueIds) }, { status });
            for (const queueId of queueIds) {
                const redisKey = `queue:${queueId}`;
                const redisData = await this.redisService.get(redisKey);
                if (redisData) {
                    await this.redisService.updateQueueStatus(queueId.toString(), status);
                }
            }
            const dateStr = typeof date === 'string' ? date : new Date().toISOString().split('T')[0];
            await this.redisService.invalidateServiceQueues(serviceId, dateStr);
            await this.redisService.invalidateServiceQueues(serviceId, 'all');
            const queueCountsKey = `service:${serviceId}:queue-counts:${dateStr}`;
            await this.redisService.del(queueCountsKey);
            const activeQueuesKey = `service:${serviceId}:active-queues:${dateStr}`;
            const allActiveQueuesKey = `service:${serviceId}:active-queues:all`;
            await this.redisService.del(activeQueuesKey);
            await this.redisService.del(allActiveQueuesKey);
            console.log(`Successfully updated ${queueIds.length} queues to status '${status}'`);
        }
        catch (error) {
            console.error(`Error updating queue status: ${error.message}`);
        }
    }
    async getQueueByUniqueSlotId(uniqueSlotId) {
        try {
            const queue = await this.queueRepository.findOne({
                where: { uniqueSlotId },
                relations: ['service']
            });
            if (!queue) {
                throw new common_1.NotFoundException(`Queue with uniqueSlotId ${uniqueSlotId} not found`);
            }
            let mobileNumber = null;
            if (queue.userId && !queue.userId.includes('@')) {
                try {
                    const user = await this.findUserByClerkId(queue.userId);
                    if (user && user.mobileNumber) {
                        console.log(`Found mobile number via clerk ID: ${user.mobileNumber}`);
                        mobileNumber = user.mobileNumber;
                    }
                }
                catch (error) {
                    console.error(`Error looking up user by clerk ID: ${error.message}`);
                }
            }
            const result = {
                ...queue,
                serviceName: queue.service?.serviceName || 'Unknown Service',
                serviceType: queue.service?.serviceType || 'Unknown Type',
                mobileNumber
            };
            try {
                const redisData = await this.redisService.getQueue(queue.id.toString());
                if (redisData) {
                    return {
                        ...redisData,
                        ...result,
                        uniqueSlotId: queue.uniqueSlotId,
                        userName: queue.userName,
                        status: queue.status,
                        mobileNumber: mobileNumber || redisData.mobileNumber
                    };
                }
            }
            catch (error) {
                console.log(`Error fetching Redis data for queue ${queue.id}:`, error);
            }
            return result;
        }
        catch (error) {
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            console.error(`Error in getQueueByUniqueSlotId(${uniqueSlotId}):`, error);
            throw new Error(`Failed to fetch queue by uniqueSlotId: ${error.message}`);
        }
    }
    async getAllQueuesFromRedis(serviceId) {
        try {
            console.log(`Fetching all queues from Redis for service ID: ${serviceId}`);
            const activeQueues = await this.getServiceActiveQueues(serviceId, 'all');
            console.log(`Found ${activeQueues.length} active queues for service ${serviceId}`);
            const completedQueues = await this.getServiceQueuesByStatus(serviceId, 'all', 'completed');
            console.log(`Found ${completedQueues.length} completed queues for service ${serviceId}`);
            const noShowQueues = await this.getServiceQueuesByStatus(serviceId, 'all', 'no-show');
            console.log(`Found ${noShowQueues.length} no-show queues for service ${serviceId}`);
            let allQueues = [...activeQueues, ...completedQueues, ...noShowQueues];
            if (allQueues.length === 0) {
                console.log(`No queues found in Redis. Falling back to database for service ${serviceId}`);
                const dbQueues = await this.queueRepository.find({
                    where: { serviceId },
                    relations: ['service'],
                });
                console.log(`Found ${dbQueues.length} queues in database for service ${serviceId}`);
                allQueues = dbQueues.map(queue => ({
                    id: queue.id,
                    serviceId: queue.serviceId,
                    serviceName: queue.service?.serviceName || 'Unknown Service',
                    serviceType: queue.service?.serviceType || 'Unknown Type',
                    date: queue.date,
                    timeSlot: queue.timeSlot,
                    status: queue.status,
                    isVIP: queue.isVIP,
                    userId: queue.userId,
                    createdAt: queue.createdAt,
                    uniqueSlotId: queue.uniqueSlotId || Math.floor(1000 + Math.random() * 9000).toString(),
                    isCheckedIn: queue.isCheckedIn || false
                }));
                for (const queue of allQueues) {
                    await this.redisService.saveQueue(queue.id.toString(), queue);
                }
            }
            const uniqueQueues = Array.from(new Map(allQueues.map(queue => [queue.id, queue])).values());
            console.log(`Returning ${uniqueQueues.length} unique queues for service ${serviceId}`);
            return uniqueQueues;
        }
        catch (error) {
            console.error(`Error fetching all queues from Redis for service ${serviceId}:`, error);
            throw new Error(`Failed to fetch all queues from Redis: ${error.message}`);
        }
    }
    async getAllQueuesDirectFromRedis(serviceId) {
        try {
            console.log(`Fast Redis fetch for service ID: ${serviceId}`);
            const redisKeyPattern = `queue:*`;
            const allKeys = await this.redisService.getKeys(redisKeyPattern);
            if (!allKeys || allKeys.length === 0) {
                console.log(`No queue keys found in Redis`);
                return this.getAllQueuesFromDatabase(serviceId);
            }
            console.log(`Found ${allKeys.length} total queue keys in Redis`);
            const startTime = Date.now();
            const allValues = await this.redisService.mget(...allKeys);
            const fetchTime = Date.now();
            console.log(`Redis batch fetch completed in ${fetchTime - startTime}ms`);
            const filteredQueues = allValues
                .filter(Boolean)
                .filter(queue => queue.serviceId === serviceId);
            const filterTime = Date.now();
            console.log(`Memory filtering completed in ${filterTime - fetchTime}ms (found ${filteredQueues.length} queues)`);
            if (filteredQueues.length === 0) {
                console.log(`No queues found in Redis for service ${serviceId}. Falling back to database.`);
                return this.getAllQueuesFromDatabase(serviceId);
            }
            return filteredQueues;
        }
        catch (error) {
            console.error(`Error in direct Redis fetch for service ${serviceId}:`, error);
            console.log(`Falling back to database due to Redis error`);
            return this.getAllQueuesFromDatabase(serviceId);
        }
    }
    async getAllQueuesFromDatabase(serviceId) {
        console.log(`Fetching all queues from database for service ${serviceId}`);
        const startTime = Date.now();
        const queues = await this.queueRepository.find({
            where: { serviceId },
            relations: ['service'],
        });
        const endTime = Date.now();
        console.log(`Database query completed in ${endTime - startTime}ms (found ${queues.length} queues)`);
        if (queues.length === 0) {
            return [];
        }
        const mappedQueues = queues.map(queue => ({
            id: queue.id,
            serviceId: queue.serviceId,
            serviceName: queue.service?.serviceName || 'Unknown Service',
            serviceType: queue.service?.serviceType || 'Unknown Type',
            date: queue.date,
            timeSlot: queue.timeSlot,
            status: queue.status,
            isVIP: queue.isVIP,
            userId: queue.userId,
            createdAt: queue.createdAt,
            uniqueSlotId: queue.uniqueSlotId || Math.floor(1000 + Math.random() * 9000).toString(),
            isCheckedIn: queue.isCheckedIn || false,
            hasSubUnits: queue.hasSubUnits || false,
            subUnitId: queue.subUnitId || '',
            subUnitName: queue.subUnitName || ''
        }));
        const cacheStart = Date.now();
        for (const queue of mappedQueues) {
            await this.redisService.saveQueue(queue.id.toString(), queue);
        }
        const cacheEnd = Date.now();
        console.log(`Redis caching completed in ${cacheEnd - cacheStart}ms`);
        return mappedQueues;
    }
    async ensureQueueConsistency(serviceId) {
        try {
            const allKeys = await this.redisService.getKeys(`queue:*`);
            console.log(`Found ${allKeys.length} total queue keys in Redis`);
            if (!allKeys || allKeys.length === 0) {
                console.log(`No queue keys found in Redis`);
                return;
            }
            const allQueueData = await this.redisService.mget(...allKeys);
            const serviceQueues = allQueueData
                .filter(q => q && q.serviceId === serviceId)
                .map(q => ({
                id: q.id,
                status: q.status,
                lastUpdated: q.statusUpdatedAt || q.updatedAt || null
            }));
            console.log(`Found ${serviceQueues.length} queues for service ${serviceId} in Redis`);
            if (serviceQueues.length > 0) {
                const queueIds = serviceQueues.map(q => q.id);
                const dbQueues = await this.queueRepository.find({
                    where: { id: (0, typeorm_2.In)(queueIds) },
                    select: ['id', 'status', 'isCheckedIn', 'date', 'timeSlot', 'serviceId']
                });
                console.log(`Found ${dbQueues.length} matching queues in database`);
                const fixPromises = dbQueues.map(async (dbQueue) => {
                    const redisQueue = serviceQueues.find(q => q.id === dbQueue.id);
                    if (!redisQueue)
                        return;
                    if (redisQueue.status !== dbQueue.status) {
                        console.log(`Status mismatch for queue ${dbQueue.id}: Redis=${redisQueue.status}, DB=${dbQueue.status}`);
                        if (dbQueue.status === 'completed' || dbQueue.status === 'no-show') {
                            console.log(`Fixing Redis status for queue ${dbQueue.id} to match DB: ${dbQueue.status}`);
                            const fullRedisData = await this.redisService.getQueue(dbQueue.id);
                            if (fullRedisData) {
                                const updatedData = {
                                    ...fullRedisData,
                                    status: dbQueue.status,
                                    statusUpdatedAt: new Date().toISOString(),
                                    statusFixed: true
                                };
                                await this.redisService.saveQueue(dbQueue.id, updatedData, 86400 * 7);
                                const statusKey = `queue:${dbQueue.id}:status:${dbQueue.status}`;
                                await this.redisService.set(statusKey, updatedData, { ex: 86400 * 14 });
                            }
                        }
                        else if ((redisQueue.status === 'completed' || redisQueue.status === 'no-show') &&
                            dbQueue.status !== redisQueue.status) {
                            console.log(`Fixing DB status for queue ${dbQueue.id} to match Redis: ${redisQueue.status}`);
                            dbQueue.status = redisQueue.status;
                            await this.queueRepository.save(dbQueue);
                        }
                    }
                });
                await Promise.all(fixPromises);
                console.log(`Completed consistency check for ${dbQueues.length} queues`);
            }
        }
        catch (error) {
            console.error(`Error in ensureQueueConsistency for service ${serviceId}:`, error);
        }
    }
    async restoreQueueData(serviceId) {
        try {
            const dbQueues = await this.queueRepository.find({
                where: { serviceId },
                relations: ['service'],
            });
            console.log(`Found ${dbQueues.length} queues for service ${serviceId} in database`);
            if (dbQueues.length === 0)
                return;
            const existingKeys = await this.redisService.getKeys(`queue:*`);
            const existingQueueIds = existingKeys
                .map(key => {
                const match = key.match(/queue:(\d+)$/);
                return match ? parseInt(match[1]) : null;
            })
                .filter(id => id !== null);
            console.log(`Found ${existingQueueIds.length} existing queue keys in Redis`);
            let restoredCount = 0;
            const restorePromises = dbQueues.map(async (queue) => {
                if (!existingQueueIds.includes(queue.id)) {
                    const redisData = {
                        id: queue.id,
                        serviceId: queue.serviceId,
                        serviceName: queue.service?.serviceName || 'Unknown Service',
                        serviceType: queue.service?.serviceType || 'Unknown Type',
                        date: queue.date,
                        timeSlot: queue.timeSlot,
                        status: queue.status,
                        isVIP: queue.isVIP,
                        userId: queue.userId,
                        createdAt: queue.createdAt,
                        updatedAt: new Date().toISOString(),
                        uniqueSlotId: queue.uniqueSlotId || Math.floor(1000 + Math.random() * 9000).toString(),
                        isCheckedIn: queue.isCheckedIn,
                        fullName: queue.userName || 'Anonymous',
                        mobileNumber: null,
                        statusUpdatedAt: new Date().toISOString(),
                        restoredFromDB: true
                    };
                    await this.redisService.saveQueue(queue.id.toString(), redisData, 86400 * 7);
                    const statusKey = `queue:${queue.id}:status:${queue.status}`;
                    await this.redisService.set(statusKey, redisData, { ex: 86400 * 14 });
                    restoredCount++;
                }
            });
            await Promise.all(restorePromises);
            console.log(`Restored ${restoredCount} queues to Redis from database`);
        }
        catch (error) {
            console.error(`Error in restoreQueueData for service ${serviceId}:`, error);
        }
    }
    async getQueueGracePeriodStatus(queueId) {
        const redisKey = `queue:${queueId}`;
        const queueData = await this.redisService.get(redisKey);
        if (queueData) {
            const now = new Date();
            let remainingSeconds = 0;
            let isExpired = true;
            if (queueData.inGracePeriod && queueData.graceStartedAt && queueData.graceEndTime) {
                const graceEndTime = new Date(queueData.graceEndTime);
                if (now < graceEndTime) {
                    isExpired = false;
                    remainingSeconds = Math.floor((graceEndTime.getTime() - now.getTime()) / 1000);
                }
            }
            return {
                queueId: queueData.id,
                inGracePeriod: queueData.inGracePeriod || false,
                confirmedPresence: queueData.confirmedPresence || false,
                graceStartedAt: queueData.graceStartedAt,
                graceEndTime: queueData.graceEndTime,
                graceTimeSeconds: queueData.graceTimeSeconds || 120,
                remainingSeconds: queueData.inGracePeriod ? remainingSeconds : 0,
                isExpired: queueData.inGracePeriod ? isExpired : false,
                status: queueData.status
            };
        }
        const queue = await this.queueRepository.findOne({
            where: { id: queueId },
            relations: ['service']
        });
        if (!queue) {
            throw new common_1.NotFoundException(`Queue with ID ${queueId} not found`);
        }
        let graceTime = 120;
        if (queue.service) {
            const setup = await this.serviceSetupRepository.findOne({
                where: { service: { id: queue.serviceId } }
            });
            if (setup?.graceTime) {
                graceTime = setup.graceTime;
            }
        }
        let remainingSeconds = 0;
        let isExpired = true;
        if (queue.inGracePeriod && queue.graceStartedAt) {
            const now = new Date();
            const graceEndTime = new Date(queue.graceStartedAt);
            graceEndTime.setSeconds(graceEndTime.getSeconds() + graceTime);
            if (now < graceEndTime) {
                isExpired = false;
                remainingSeconds = Math.floor((graceEndTime.getTime() - now.getTime()) / 1000);
            }
        }
        return {
            queueId: queue.id,
            inGracePeriod: queue.inGracePeriod,
            confirmedPresence: queue.confirmedPresence,
            graceStartedAt: queue.graceStartedAt,
            graceTimeSeconds: graceTime,
            remainingSeconds: queue.inGracePeriod ? remainingSeconds : 0,
            isExpired: queue.inGracePeriod ? isExpired : false,
            status: queue.status
        };
    }
};
exports.CustomerService = CustomerService;
exports.CustomerService = CustomerService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(user_entity_1.User)),
    __param(1, (0, typeorm_1.InjectRepository)(queue_entity_1.Queue)),
    __param(2, (0, typeorm_1.InjectRepository)(service_entity_1.Service)),
    __param(3, (0, typeorm_1.InjectRepository)(service_setup_entity_1.ServiceSetup)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        redis_service_1.RedisService,
        core_1.ModuleRef])
], CustomerService);
//# sourceMappingURL=customer.service.js.map