import React, { useEffect, useState } from "react";
import {
  Text,
  SafeAreaView,
  View,
  Image,
  TouchableOpacity,
  StatusBar,
  ScrollView,
  ActivityIndicator,
  RefreshControl,
} from "react-native";
import * as SecureStore from "expo-secure-store";
import * as Location from "expo-location";
import { router } from "expo-router";
import { images } from "@/constants";
import SearchButton from "@/components/SearchButton";

interface LocationData {
  coordinates: {
    latitude: number;
    longitude: number;
  };
  area: string;
  fullAddress: string;
}

interface Service {
  _id: string;
  serviceName: string;
  serviceType: string;
  address: {
    details: {
      buildingNo: string;
      locality: string;
      city: string;
    };
    coordinates: {
      latitude: number;
      longitude: number;
    };
  };
  image: string | null;
  isOpen: boolean;
  queueInfo?: {
    waitingTime: number;
    membersInQueue: number;
    cost: number;
    servingTime: number;
    normalCount?: number;
    vipCount?: number;
    currentTimeSlot?: string;
  };
  distance?: string; // Add distance property
  rating?: number;
  reviewCount?: number;
}

const HomeScreen = () => {
  const [locationData, setLocationData] = useState<LocationData | null>(null);
  const [formattedAddress, setFormattedAddress] = useState<string>("");
  const [services, setServices] = useState<Service[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedCategory, setSelectedCategory] = useState<string>("All");

  const categories = [
    { name: "All", icon: images.all },
    { name: "Hospital", icon: images.hospital },
    { name: "Bank", icon: images.bank },
    { name: "Government", icon: images.government },
    { name: "Retail", icon: images.retail },
    { name: "Restaurant", icon: images.restaurant },
    { name: "Theatre", icon: images.theatre },
    { name: "Tourism", icon: images.tourism },
    { name: "Salon", icon: images.salon },
  ];

  useEffect(() => {
    loadLocationData();
  }, []);

  const loadLocationData = async () => {
    try {
      console.log("Loading location data...");
      setIsLoading(true);
      const savedLocation = await SecureStore.getItemAsync("userLocation");
      console.log("Saved location data:", savedLocation);

      if (!savedLocation) {
        console.warn("No saved location found");
        setServices([]);
        setIsLoading(false);
        return;
      }

      let parsedLocation;
      try {
        parsedLocation = JSON.parse(savedLocation);
        console.log("Parsed location:", parsedLocation);

        // Validate required fields
        if (
          !parsedLocation?.coordinates?.latitude ||
          !parsedLocation?.coordinates?.longitude
        ) {
          throw new Error("Invalid location coordinates");
        }

        // Immediately set location data to use in fetchServices
        setLocationData({
          coordinates: {
            latitude: parsedLocation.coordinates.latitude,
            longitude: parsedLocation.coordinates.longitude,
          },
          area: parsedLocation.area || "",
          fullAddress: parsedLocation.fullAddress || "",
        });

        // Format address after validation
        const addressParts = parsedLocation.fullAddress.split(",");
        let address = "";
        if (addressParts.length >= 3) {
          address = `${addressParts[1].trim()}, ${addressParts[2].trim()}`;
        } else if (addressParts.length === 2) {
          address = addressParts[1].trim();
        } else {
          address = parsedLocation.area;
        }

        setFormattedAddress(
          address.length > 18 ? address.substring(0, 20) + "..." : address
        );

        // Fetch services after location data is set
        const servicesData = await fetchServices(parsedLocation.coordinates);
        if (servicesData) {
          setServices(servicesData);
        }
      } catch (parseError) {
        console.error("Error parsing location data:", parseError);
        console.log("Invalid location data:", savedLocation);
      }
    } catch (error) {
      console.error("Error loading location data:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const calculateDistances = async (
    services: Service[],
    userLat: number,
    userLng: number
  ) => {
    try {
      // Validate user coordinates
      if (!userLat || !userLng) {
        console.error("Invalid user coordinates:", { userLat, userLng });
        return services;
      }

      // Filter services with valid coordinates
      const validServices = services.filter((service) => {
        const hasCoords =
          service.address?.coordinates?.latitude &&
          service.address?.coordinates?.longitude;
        if (!hasCoords) {
          console.warn("Missing coordinates for service:", service._id);
        }
        return hasCoords;
      });

      if (validServices.length === 0) {
        console.warn("No services with valid coordinates");
        return services;
      }

      const origins = `${userLat},${userLng}`;
      const destinations = validServices
        .map(
          (service) =>
            `${service.address.coordinates.latitude},${service.address.coordinates.longitude}`
        )
        .join("|");

      console.log("Distance matrix request:", { origins, destinations });

      const response = await fetch(
        `https://maps.googleapis.com/maps/api/distancematrix/json?origins=${origins}&destinations=${destinations}&key=AIzaSyAUIQg4PbIdD4MuC2PWJbo1W7njP902gpI`
      );

      const data = await response.json();
      console.log("Distance matrix response:", data);

      if (data.rows?.[0]?.elements) {
        return services.map((service, index) => {
          const distanceInfo = data.rows[0].elements[index];
          return {
            ...service,
            distance: distanceInfo?.distance?.text || "N/A",
          };
        });
      }

      return services;
    } catch (error) {
      console.error("Error calculating distances:", error);
      return services;
    }
  };

  const fetchServices = async (coordinates = locationData?.coordinates) => {
    try {
      if (!coordinates?.latitude || !coordinates?.longitude) {
        console.warn("No valid location coordinates available");
        return null;
      }

      console.log("Fetching services with coordinates:", coordinates);

      const response = await fetch(
        "http://***************:3000/api/partner/services"
      );

      if (!response.ok) {
        throw new Error("Failed to fetch services");
      }

      let data = await response.json();
      console.log("Services fetched successfully");

      // Get accurate queue counts for each service's current/next time slot
      const today = new Date().toISOString().split('T')[0];

      // Update queue info for each service with accurate counts and fetch subunit prices if needed
      data = await Promise.all(data.map(async (service: Service) => {
        try {
          // First check if service has subunits by fetching service setup data
          let hasSubUnits = false;
          let firstSubUnitPrice = null;

          try {
            const setupResponse = await fetch(
              `http://***************:3000/api/partner/service-setup/${service._id}`
            );

            if (setupResponse.ok) {
              const setupData = await setupResponse.json();
              console.log(`Service setup data for ${service._id}:`, setupData.hasSetup ? "Available" : "Not available");

              if (setupData.status === "success" && setupData.hasSetup && setupData.data) {
                // Check if service has subunits
                if (
                  setupData.data.hasSubUnits === true &&
                  Array.isArray(setupData.data.subUnits) &&
                  setupData.data.subUnits.length > 0
                ) {
                  hasSubUnits = true;
                  // Get the price of the first subunit
                  firstSubUnitPrice = setupData.data.subUnits[0].pricePerHead;
                  console.log(`Service ${service._id} has subunits. Using first subunit price: ${firstSubUnitPrice}`);

                  // Update the service price with the first subunit's price
                  if (service.queueInfo && firstSubUnitPrice) {
                    service.queueInfo.cost = parseInt(firstSubUnitPrice);
                  }
                }
              }
            }
          } catch (error) {
            console.error(`Error checking subunits for service ${service._id}:`, error);
            // Continue with default price if this fails
          }

          // Update the queue counts
          if (service.queueInfo) {
            const queueInfo = await fetchRedisQueueCounts(service._id);
            if (queueInfo) {
              service.queueInfo.normalCount = queueInfo.normalCount;
              service.queueInfo.vipCount = queueInfo.vipCount;
              // Store the current/next time slot
              service.queueInfo.currentTimeSlot = queueInfo.timeSlot;
              // Update total members in queue (combined normal + VIP count)
              service.queueInfo.membersInQueue = queueInfo.normalCount + queueInfo.vipCount;
              // Update waiting time based on updated queue count and serving time
              service.queueInfo.waitingTime = service.queueInfo.servingTime * (queueInfo.normalCount + queueInfo.vipCount);
            } else {
              // If no queue data is available, set default values
              service.queueInfo.normalCount = 0;
              service.queueInfo.vipCount = 0;
              service.queueInfo.membersInQueue = 0;
              service.queueInfo.currentTimeSlot = '';
              // Keep the original waiting time calculation
            }
          }
        } catch (error) {
          console.error(`Error updating queue counts for service ${service._id}:`, error);
        }
        return service;
      }));

      // Calculate distances with validated coordinates
      return await calculateDistances(
        data,
        coordinates.latitude,
        coordinates.longitude
      );
    } catch (error) {
      console.error("Error fetching services:", error);
      return null;
    }
  };

  const handleLocationPress = async () => {
    try {
      const { status } = await Location.getForegroundPermissionsAsync();
      if (status !== "granted") {
        router.push("/(location)/allow-location");
      } else {
        router.push("/(location)/select-location");
      }
    } catch (error) {
      console.error("Error checking location permission:", error);
    }
  };

  const onRefresh = React.useCallback(async () => {
    setRefreshing(true);
    try {
      const servicesData = await fetchServices();
      if (servicesData) {
        setServices(servicesData);
      }
    } catch (error) {
      console.error("Error refreshing services:", error);
    } finally {
      setRefreshing(false);
    }
  }, [locationData]);

  const filteredServices = React.useMemo(() => {
    let filtered = services;

    // First filter by search query
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(
        (service) =>
          service.serviceName.toLowerCase().includes(query) ||
          service.serviceType.toLowerCase().includes(query) ||
          service.address.details.locality.toLowerCase().includes(query) ||
          service.address.details.city.toLowerCase().includes(query)
      );
    }

    // Then filter by category if not "All"
    if (selectedCategory !== "All") {
      filtered = filtered.filter(
        (service) => service.serviceType === selectedCategory
      );
    }

    return filtered;
  }, [services, searchQuery, selectedCategory]);

  // Update fetchRedisQueueCounts to only show queues for today or the nearest date
  const fetchRedisQueueCounts = async (serviceId: string): Promise<{ normalCount: number; vipCount: number; timeSlot: string } | null> => {
    try {
      console.log(`Fetching queue counts for service ${serviceId} using Redis keys`);

      // Check if service has subunits by fetching service setup data
      let hasSubUnits = false;
      let firstSubUnitIndex = 0;

      try {
        const setupResponse = await fetch(
          `http://***************:3000/api/partner/service-setup/${serviceId}`
        );

        if (setupResponse.ok) {
          const setupData = await setupResponse.json();
          console.log(`Service setup data for ${serviceId}:`, setupData.hasSetup ? "Available" : "Not available");

          if (setupData.status === "success" && setupData.hasSetup && setupData.data) {
            // Check if service has subunits
            if (
              setupData.data.hasSubUnits === true &&
              Array.isArray(setupData.data.subUnits) &&
              setupData.data.subUnits.length > 0
            ) {
              hasSubUnits = true;
              console.log(`Service ${serviceId} has ${setupData.data.subUnits.length} subunits`);
            }
          }
        }
      } catch (error) {
        console.error(`Error checking subunits for service ${serviceId}:`, error);
        // Continue with default (no subunits) if this fails
      }

      // Determine the API endpoint based on whether we're using subunits
      let apiUrl = `http://***************:3000/api/customer/queues/redis/${serviceId}/upcoming`;

      // If using subunits, add the subunit parameter for the first subunit
      if (hasSubUnits) {
        apiUrl += `?subUnitId=${firstSubUnitIndex}`;
        console.log(`Using first subunit (index: ${firstSubUnitIndex}) for queue counts in home screen`);
      }

      // Get all active queues for this service using the Redis API with /upcoming endpoint
      const response = await fetch(apiUrl);

      if (!response.ok) {
        console.error(`Failed to fetch queues from Redis: ${response.status}`);
        return null;
      }

      const data = await response.json();

      if (data.status !== "success" || !data.queues) {
        console.log("No queue data available from Redis");
        return null;
      }

      console.log(`Successfully retrieved ${data.queues.length} queues from Redis`);

      // Get today's date in YYYY-MM-DD format
      const today = new Date().toISOString().split('T')[0];

      // First, filter and group queues by date
      const dateGroups: Record<string, any[]> = {};

      data.queues.forEach((queue: any) => {
        if (!queue || !queue.date) return;

        // Extract date part in YYYY-MM-DD format
        let dateStr;
        try {
          if (typeof queue.date === 'string') {
            dateStr = queue.date.split('T')[0];
          } else {
            dateStr = new Date(queue.date).toISOString().split('T')[0];
          }
        } catch (err) {
          console.error("Error parsing queue date:", err);
          return;
        }

        if (!dateGroups[dateStr]) {
          dateGroups[dateStr] = [];
        }
        dateGroups[dateStr].push(queue);
      });

      // If no dates with queues, return null
      if (Object.keys(dateGroups).length === 0) {
        console.log("No dates with queues found");
        return { normalCount: 0, vipCount: 0, timeSlot: '' };
      }

      // Sort dates and find the earliest one (today or future)
      const availableDates = Object.keys(dateGroups)
        .filter(date => date >= today)
        .sort();

      // If no future dates, return empty result
      if (availableDates.length === 0) {
        console.log("No future dates with queues found");
        return { normalCount: 0, vipCount: 0, timeSlot: '' };
      }

      // Get the earliest date
      const earliestDate = availableDates[0];
      console.log(`Using queues for date: ${earliestDate}`);

      // Only consider queues for this date
      const relevantQueues = dateGroups[earliestDate];

      // Group queues by time slot
      const timeSlotGroups: Record<string, any[]> = {};
      relevantQueues.forEach((queue: any) => {
        if (queue && queue.timeSlot) {
          if (!timeSlotGroups[queue.timeSlot]) {
            timeSlotGroups[queue.timeSlot] = [];
          }
          timeSlotGroups[queue.timeSlot].push(queue);
        }
      });

      // Get the current time in 24 hour format
      const now = new Date();
      const currentHours = now.getHours();
      const currentMinutes = now.getMinutes();
      const currentTime = currentHours * 60 + currentMinutes; // in minutes

      // Find current or next time slot
      let selectedTimeSlot = '';
      let selectedTimeSlotQueues: any[] = [];

      const timeSlots = Object.keys(timeSlotGroups);
      if (timeSlots.length > 0) {
        // Convert time slots to comparable format and find current or next slot
        const timeSlotTimes = timeSlots.map(slot => {
          const parts = slot.split(' - ');
          const startTimePart = parts[0].trim();
          const [timeValue, period] = startTimePart.split(' ');
          const [hourStr, minuteStr] = timeValue.split(':');
          let hour = parseInt(hourStr, 10);
          const minute = parseInt(minuteStr, 10);

          if (period.toUpperCase() === 'PM' && hour < 12) {
            hour += 12;
          } else if (period.toUpperCase() === 'AM' && hour === 12) {
            hour = 0;
          }

          return {
            slot,
            time: hour * 60 + minute
          };
        });

        // Sort by time
        timeSlotTimes.sort((a, b) => a.time - b.time);

        // If today, find current time slot, otherwise use the first time slot of the earliest future date
        if (earliestDate === today) {
          // For today, find the current time slot
          let selectedSlotInfo = timeSlotTimes[0]; // Default to first

          // First, try to find a time slot that contains the current time
          for (let i = 0; i < timeSlotTimes.length; i++) {
            const currentSlot = timeSlotTimes[i];
            const nextSlot = i < timeSlotTimes.length - 1 ? timeSlotTimes[i + 1] : null;

            // Check if current time is within this slot's range
            // For the last slot or if current time is before the next slot starts
            if (!nextSlot || currentTime < nextSlot.time) {
              if (currentTime >= currentSlot.time) {
                selectedSlotInfo = currentSlot;
                console.log(`Found current time slot: ${currentSlot.slot} (current time: ${currentHours}:${currentMinutes})`);
                break;
              }
            }
          }

          // If no current slot found (e.g., we're between slots or before all slots),
          // find the next upcoming slot
          if (selectedSlotInfo.time > currentTime) {
            console.log(`No current time slot active, using next slot: ${selectedSlotInfo.slot}`);
          }

          selectedTimeSlot = selectedSlotInfo.slot;
        } else {
          // For future date, use the first time slot
          selectedTimeSlot = timeSlotTimes[0].slot;
          console.log(`Using first time slot for future date: ${selectedTimeSlot}`);
        }

        selectedTimeSlotQueues = timeSlotGroups[selectedTimeSlot] || [];
      }

      // Count normal and VIP queues for the selected time slot
      const normalCount = selectedTimeSlotQueues.filter((q: any) =>
        q &&
        q.isVIP !== true &&
        q.status !== 'cancelled' &&
        q.status !== 'completed'
      ).length;

      const vipCount = selectedTimeSlotQueues.filter((q: any) =>
        q &&
        q.isVIP === true &&
        q.status !== 'cancelled' &&
        q.status !== 'completed'
      ).length;

      console.log(`Current/next time slot for ${earliestDate}: ${selectedTimeSlot}, Normal: ${normalCount}, VIP: ${vipCount}`);

      return { normalCount, vipCount, timeSlot: selectedTimeSlot };
    } catch (error) {
      console.error(`Error fetching Redis queue counts for service ${serviceId}:`, error);
      return null;
    }
  };

  return (
    <SafeAreaView className="flex-1 bg-white">
      <StatusBar barStyle="dark-content" backgroundColor="white" />

      {/* Header Section */}
      <View className="px-6 pt-12">
        <View className="flex-row items-center justify-between">
          <View className="flex-1">
            <Text className="font-poppins-regular text-[14px] ml-4 -mb-4 text-secondary-600">
              Location
            </Text>
            <TouchableOpacity
              className="flex-row items-center p-4 rounded-xl"
              onPress={handleLocationPress}
            >
              <Image
                source={images.smallPin}
                className="w-[13px] h-[15px] mr-3"
              />
              <Text className="font-poppins-medium text-[14px] text-black">
                {formattedAddress || "Loading..."}
              </Text>
              <Image
                source={images.down}
                className="w-[16px] h-[10px] ml-2"
                tintColor="#000000"
              />
            </TouchableOpacity>
          </View>

          <View className="flex-row items-center">
            <TouchableOpacity
              className="bg-warning-500 px-3 py-3 rounded-xl flex-row items-center mr-4"
              onPress={() => router.push("/(vip)/join-vip")}
            >
              <Text className="font-poppins-medium text-sm text-white mr-3">
                {" "}
                Join VIP
              </Text>
              <Image source={images.vip} className="w-4 h-4" />
            </TouchableOpacity>

            <TouchableOpacity
              className="bg-primary-200 p-2 rounded-full"
              onPress={() => router.push("/(notifications)/notifications")}
            >
              <Image source={images.notification} className="w-7 h-7" />
            </TouchableOpacity>
          </View>
        </View>
      </View>

      {/* Add SearchButton below header */}
      <View className="px-10 mt-4">
        <SearchButton
          value={searchQuery}
          onChangeText={setSearchQuery}
          icon={images.search}
          placeholder="Search services, locations..."
          className="w-full mb-6"
        />
      </View>

      {/* Scrollable Content */}
      <ScrollView
        className="flex-1"
        contentContainerStyle={{
          paddingBottom: 120,
        }}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            tintColor="#159AFF"
            colors={["#159AFF"]}
          />
        }
      >
        <View className="px-10">
          <Text className="font-poppins-medium text-xl mb-6">
            Available Services
          </Text>
          {/* Categories */}
          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            className=""
            contentContainerStyle={{ paddingRight: 10, paddingBottom: 30 }} // Add padding to see last item
          >
            {categories.map((category) => (
              <TouchableOpacity
                key={category.name}
                onPress={() => setSelectedCategory(category.name)}
                className={`mr-4 px-4 py-2 h-12 rounded-xl border flex-row items-center ${
                  selectedCategory === category.name
                    ? "bg-primary-500 border-primary-500"
                    : "bg-white border-primary-500/20"
                }`}
              >
                <Image
                  source={category.icon}
                  className="w-5 h-5 mr-2"
                  tintColor={
                    selectedCategory === category.name ? "#fff" : "#159AFF"
                  }
                />
                <Text
                  className={`font-poppins-medium text-sm ${
                    selectedCategory === category.name
                      ? "text-white"
                      : "text-secondary-600"
                  }`}
                >
                  {category.name}
                </Text>
              </TouchableOpacity>
            ))}
          </ScrollView>

          {isLoading ? (
            <ActivityIndicator size="large" color="#159AFF" />
          ) : filteredServices.length === 0 ? (
            <Text className="text-center text-secondary-600 mt-4">
              {searchQuery
                ? "No matching services found"
                : "No services available"}
            </Text>
          ) : (
            <View>
              {filteredServices.map((service, index) => (
                <TouchableOpacity
                  key={service._id}
                  className={`bg-white rounded-3xl border border-primary-200 overflow-hidden drop-shadow-lg shadow-lg shadow-primary-300 ${
                    index !== filteredServices.length - 1 ? "mb-8" : ""
                  }`}
                  onPress={() =>
                    router.push({
                      pathname: "/(root)/service-details",
                      params: { id: service._id },
                    })
                  }
                >
                  <View className="p-4 w-full">
                    <View className="flex-row justify-center items-center">
                      <Image
                        source={
                          service.image
                            ? { uri: service.image }
                            : images.profile
                        }
                        className="w-20 h-20 rounded-xl"
                        style={{ backgroundColor: "#f5f5f5" }}
                      />
                      <View className="flex-1 p-3">
                        <View className="flex-row justify-between items-start">
                          <View className="flex-row flex-wrap items-center mr-2 flex-1">
                            <Text
                              className="font-poppins-medium text-lg"
                              numberOfLines={2}
                              style={{ maxWidth: "85%" }}
                            >
                              {service.serviceName}
                            </Text>
                            {/* Rating display */}
                            {service.rating ? (
                              <View className="flex-row items-center  bg-warning-500/10 rounded-lg px-[5px] py-[3px] ml-2">
                                <Image
                                  source={images.star}
                                  className="w-[10px] h-[10px] mr-1 mb-1"
                                />
                                <Text className="text-warning-500 font-poppins-medium text-[10px]">
                                  {service.rating.toFixed(1)}
                                </Text>
                              </View>
                            ) : null}
                          </View>

                          <View className="flex-row items-center mr-1">
                            <Image
                              source={images.mapPin}
                              className="w-4 h-4 mr-1"
                              tintColor="#159AFF"
                            />
                            <Text className="text-secondary-600 mt-1 text-xs font-poppins-medium">
                              {service.distance || "N/A"}
                            </Text>
                          </View>
                        </View>

                        <Text className="text-secondary-600 text-sm mt-1">
                          {service.address.details.locality},{" "}
                          {service.address.details.city}
                        </Text>
                      </View>
                    </View>
                    <View className="flex-row pt-4 px-1 justify-between mb-1">
                      <View className="flex-row bg-primary-500/5 p-3 px-6 rounded-xl items-center justify-center">
                        <View className="flex-row items-center mr-6">
                          <Image
                            source={images.time}
                            className="w-5 h-5 mr-2"
                            tintColor="#159AFF"
                          />
                          <Text className="text-secondary-600 text-sm">
                            {`${service.queueInfo?.servingTime}m per person`}
                          </Text>
                        </View>
                        <View className="flex-row items-center">
                          <Image
                            source={images.run}
                            className="w-4 h-5 mr-2"
                            tintColor="#159AFF"
                          />
                          <Text className="text-secondary-600 text-sm">
                            {service.queueInfo?.membersInQueue || 0} in queue
                          </Text>

                        </View>
                      </View>
                      <View className="flex-row items-center  px-3 rounded-xl">
                        <Text className="text-primary-500 font-poppins-medium">
                          ₹{service.queueInfo?.cost}
                        </Text>
                      </View>
                    </View>
                  </View>
                </TouchableOpacity>
              ))}
            </View>
          )}
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

export default HomeScreen;
