import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  TextInput,
  ScrollView,
  TouchableOpacity,
  Image,
  StatusBar,
  SafeAreaView,
  Alert,
  Platform,
  Linking,
} from "react-native";
import { useRouter } from "expo-router";
import Stepper from "../../components/Stepper";
import { useUser } from "@clerk/clerk-expo";
import * as SecureStore from "expo-secure-store";
import { images } from "@/constants";
import ButtonBlueMain from "@/components/ButtonBlueMain";
import * as ImagePicker from "expo-image-picker";

export default function DocumentsScreen() {
  const router = useRouter();
  const { user } = useUser();
  const [formData, setFormData] = useState({
    panNumber: "",
    gstin: "",
  });
  const [panCardImage, setPanCardImage] = useState<{ uri: string; base64: string | null | undefined } | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false); // Add this line

  useEffect(() => {
    loadSavedData();
  }, []);

  const loadSavedData = async () => {
    try {
      const savedDocData = await SecureStore.getItemAsync("documentData");
      const savedPanImage = await SecureStore.getItemAsync("panCardImage");
      if (savedDocData) {
        setFormData(JSON.parse(savedDocData));
      }
      if (savedPanImage) {
        try {
          setPanCardImage(JSON.parse(savedPanImage));
        } catch (error) {
          // Handle legacy string format
          setPanCardImage({ uri: savedPanImage, base64: null });
        }
      }
    } catch (error) {
      console.error("Error loading saved data:", error);
    }
  };

  useEffect(() => {
    SecureStore.setItemAsync("documentData", JSON.stringify(formData));
  }, [formData]);

  const validateForm = () => {
    if (!formData.panNumber.trim()) {
      Alert.alert("Error", "Please enter your PAN card number");
      return false;
    }
    if (!panCardImage) {
      Alert.alert("Error", "Please upload your PAN card photo");
      return false;
    }
    return true;
  };

  const requestPermission = async () => {
    const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
    if (status !== "granted") {
      Alert.alert(
        "Permission Required",
        "Sorry, we need camera roll permissions to upload images.",
        [
          { text: "Cancel", style: "cancel" },
          { text: "Settings", onPress: () => Linking.openSettings() },
        ]
      );
      return false;
    }
    return true;
  };

  const handlePanCardUpload = async () => {
    const hasPermission = await requestPermission();
    if (!hasPermission) return;

    try {
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        quality: 0.7,
        base64: true,
      });

      if (!result.canceled && result.assets?.[0]) {
        // Match format exactly like step1.tsx
        const imageData = {
          uri: result.assets[0].uri,
          base64: result.assets[0].base64
        };
        setPanCardImage(imageData);
        await SecureStore.setItemAsync("panCardImage", JSON.stringify(imageData));
      }
    } catch (error) {
      console.error("Error picking image:", error);
      Alert.alert("Error", "Failed to upload PAN card image");
    }
  };

  const submitServiceData = async () => {
    if (isSubmitting) return; // Prevent duplicate submissions
    
    try {
      setIsSubmitting(true); // Set loading state
      // Get all data from secure storage
      const [serviceFormData, serviceImages, serviceAddress, documentData, panCardImage] = await Promise.all([
        SecureStore.getItemAsync("serviceFormData"),
        SecureStore.getItemAsync("serviceImages"), 
        SecureStore.getItemAsync("serviceAddress"),
        SecureStore.getItemAsync("documentData"),
        SecureStore.getItemAsync("panCardImage")
      ]);

      if (!serviceFormData || !serviceImages || !serviceAddress || !documentData || !panCardImage) {
        throw new Error("Missing required data");
      }

      let parsedPanCardImage;
      try {
        parsedPanCardImage = JSON.parse(panCardImage);
      } catch {
        // Handle legacy string format
        parsedPanCardImage = { uri: panCardImage, base64: null };
      }

      // Parse service form data
      const parsedServiceFormData = JSON.parse(serviceFormData);

      // Validate all required fields are present
      const requiredFields = ['serviceName', 'serviceType', 'businessPhone', 'serviceDescription'];
      for (const field of requiredFields) {
        if (!parsedServiceFormData[field]?.trim()) {
          throw new Error(`${field.charAt(0).toUpperCase() + field.slice(1)} is required`);
        }
      }

      // Prepare data for API
      const serviceData = {
        serviceName: parsedServiceFormData.serviceName.trim(),
        serviceType: parsedServiceFormData.serviceType,
        businessPhone: parsedServiceFormData.businessPhone.trim(),
        serviceDescription: parsedServiceFormData.serviceDescription.trim(),
        address: JSON.parse(serviceAddress),
        images: JSON.parse(serviceImages),
        documents: {
          ...JSON.parse(documentData),
          panCardImage: parsedPanCardImage // Send raw parsed image data
        },
        email: parsedServiceFormData.email,
        verificationStatus: 'pending'
      };

      // Send to backend
      const response = await fetch('http://***************:3000/api/partner/register-service', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(serviceData)
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || 'Failed to submit service data');
      }

      // Store service ID and update status
      await Promise.all([
        SecureStore.setItemAsync('serviceId', data.serviceId.toString()),
        SecureStore.setItemAsync("serviceStatus", JSON.stringify({
          infoCompleted: true,
          submitted: true,
          verificationStatus: 'pending',
          submissionDate: new Date().toISOString()
        }))
      ]);

      router.replace("/service-setup/step3");

    } catch (error) {
      console.error("Error submitting service data:", error);
      Alert.alert("Error", error instanceof Error ? error.message : "Failed to submit service information");
    } finally {
      setIsSubmitting(false); // Reset loading state
    }
  };

  const handleNext = () => {
    if (!validateForm() || isSubmitting) return; // Add isSubmitting check

    Alert.alert(
      "Confirm Submission",
      "Are you sure you want to submit your application? You won't be able to edit details once submitted.",
      [
        {
          text: "Cancel",
          style: "cancel"
        },
        {
          text: "Confirm",
          style: "destructive",
          onPress: submitServiceData
        }
      ]
    );
  };

  return (
    <SafeAreaView className="flex-1 bg-white">
      <StatusBar barStyle="dark-content" backgroundColor="white" />
      <View className="px-8 pt-8 pb-4 border-b border-secondary-400">
        <View className="">
          <View className="w-full flex-row">
            <TouchableOpacity
              className="flex-row items-center absolute left-0 border border-secondary-600 w-12 h-12 rounded-full justify-center"
              onPress={() => router.back()}
            >
              <Image source={images.back} className="w-4 h-4" />
            </TouchableOpacity>
            <View className="flex-1 items-center mt-2 mb-4">
              <Text className="font-poppins-medium text-2xl mb-4">
                Service Verification
              </Text>
              <View className="w-[300px]">
                <Text className="font-poppins-regular text-center text-sm text-secondary-500/50">
                  Complete the verification process to get your service verified
                </Text>
              </View>
            </View>
          </View>
        </View>
        <Stepper
          currentStep={2}
          steps={["Service Info", "Documents", "Review"]}
        />
      </View>

      <ScrollView className="flex-1 bg-white px-4 py-2">
        <View className="p-5">
          <Text className="text-sm mb-1 text-secondary-600 font-[Poppins-Medium]">
            PAN Card Number
          </Text>
          <TextInput
            className="border border-gray-300 rounded-xl p-4 mt-2 mb-6 font-[Poppins-Regular]"
            value={formData.panNumber}
            onChangeText={(text) =>
              setFormData({ ...formData, panNumber: text })
            }
            autoCapitalize="characters"
            placeholder="Enter PAN card number"
            placeholderTextColor="#9CA3AF"
          />
          <Text className="text-sm mb-1 text-secondary-600 font-[Poppins-Medium]">
            GSTIN (Optional)
          </Text>
          <TextInput
            className="border border-gray-300 rounded-xl p-4 mt-2 mb-6 font-[Poppins-Regular]"
            value={formData.gstin}
            onChangeText={(text) => setFormData({ ...formData, gstin: text })}
            autoCapitalize="characters"
            placeholder="Enter GSTIN"
            placeholderTextColor="#9CA3AF"
          />

          <Text className="text-sm mb-1 text-secondary-600 font-[Poppins-Medium]">
            PAN Card Photo
          </Text>
          {panCardImage ? (
            <View className="mb-6">
              <Image
                source={{ uri: typeof panCardImage === 'string' ? panCardImage : panCardImage.uri }}
                className="w-full h-64 rounded-xl"
                style={{ borderWidth: 1, borderColor: "#E5E7EB" }}
              />
              <TouchableOpacity
                className="absolute top-2 right-2 bg-white rounded-full p-2"
                onPress={() => {
                  setPanCardImage(null);
                  SecureStore.deleteItemAsync("panCardImage");
                }}
              >
                <Image
                  source={images.close}
                  className="w-4 h-4"
                  style={{ tintColor: "#666" }}
                />
              </TouchableOpacity>
            </View>
          ) : (
            <TouchableOpacity
              className="flex-col justify-center bg-secondary-300/30 items-center border-2 border-dashed border-secondary-500 p-4 py-10 rounded-xl mt-2 mb-6"
              onPress={handlePanCardUpload}
            >
              <Image
                source={images.fileupload}
                className="w-20 h-20 mb-6"
                tintColor="#263238"
              />
              <Text className="text-secondary-500 text-center mb-2 text-lg font-[Poppins-Medium]">
              Upload PAN card (Front Side)
              </Text>
              <Text className="text-secondary-600 text-center text-sm font-[Poppins-Medium]">
              jpeg, png formats up-to 2MB
              </Text>
            </TouchableOpacity>
          )}
        </View>
      </ScrollView>

      <View
        className="px-4 py-4 border-t justify-center items-center border-gray-200 bg-white"
        style={{
          paddingBottom: Platform.OS === "ios" ? 20 : 15,
        }}
      >
        <ButtonBlueMain
          label={isSubmitting ? "Submitting..." : "Submit"}
          onPress={handleNext}
          bgVariant="primary"
          textVariant="primary"
          className="w-[380px] h-[80px]"
          disabled={isSubmitting}
        />
      </View>
    </SafeAreaView>
  );
}
