"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CustomerController = void 0;
const common_1 = require("@nestjs/common");
const customer_service_1 = require("./customer.service");
const join_queue_dto_1 = require("./dto/join-queue.dto");
const reschedule_queue_dto_1 = require("./dto/reschedule-queue.dto");
const queue_flow_service_1 = require("../services/queue-flow/queue-flow.service");
const scheduler_service_1 = require("../services/scheduler/scheduler.service");
const typeorm_1 = require("typeorm");
let CustomerController = class CustomerController {
    constructor(customerService, queueFlowService, schedulerService) {
        this.customerService = customerService;
        this.queueFlowService = queueFlowService;
        this.schedulerService = schedulerService;
    }
    async registerUser(body) {
        return this.customerService.createUser(body.email);
    }
    async updateLocation(data) {
        try {
            return await this.customerService.updateLocation(data.email, data.latitude, data.longitude);
        }
        catch (error) {
            throw new common_1.BadRequestException(error.message);
        }
    }
    async updateVIPStatus(data) {
        try {
            return await this.customerService.updateVIPStatus(data.email, data.isVIP);
        }
        catch (error) {
            throw new common_1.BadRequestException(error.message);
        }
    }
    async getUserProfile(email) {
        try {
            return await this.customerService.getUserProfile(email);
        }
        catch (error) {
            throw new common_1.BadRequestException(error.message);
        }
    }
    async updateUserDetails(data) {
        try {
            return await this.customerService.updateUserDetails(data.email, data.fullName, data.mobileNumber);
        }
        catch (error) {
            throw new common_1.BadRequestException(error.message);
        }
    }
    async updateClerkId(data) {
        try {
            return await this.customerService.updateClerkId(data.email, data.clerkId);
        }
        catch (error) {
            throw new common_1.BadRequestException(error.message);
        }
    }
    async addToWishlist(data) {
        try {
            return await this.customerService.addToWishlist(data.email, data.serviceId);
        }
        catch (error) {
            throw new common_1.BadRequestException(error.message);
        }
    }
    async removeFromWishlist(data) {
        try {
            return await this.customerService.removeFromWishlist(data.email, data.serviceId);
        }
        catch (error) {
            throw new common_1.BadRequestException(error.message);
        }
    }
    async getWishlist(email) {
        try {
            return await this.customerService.getWishlist(email);
        }
        catch (error) {
            throw new common_1.BadRequestException(error.message);
        }
    }
    async joinQueue(joinQueueDto) {
        try {
            const result = await this.customerService.joinQueue(joinQueueDto);
            return {
                status: 'success',
                message: 'Successfully joined the queue',
                queueId: result.id,
                serviceId: result.serviceId,
                uniqueSlotId: result.uniqueSlotId || Math.floor(1000 + Math.random() * 9000).toString(),
                timeSlot: result.timeSlot,
                date: result.date,
                hasSubUnits: result.hasSubUnits,
                subUnitId: result.subUnitId,
                subUnitName: result.subUnitName,
                position: result.position,
                timestamp: new Date().toISOString(),
            };
        }
        catch (error) {
            throw new common_1.HttpException({
                status: 'error',
                message: error.message || 'Failed to join queue',
            }, common_1.HttpStatus.BAD_REQUEST);
        }
    }
    async rescheduleQueue(rescheduleQueueDto) {
        try {
            const result = await this.customerService.rescheduleQueue(rescheduleQueueDto);
            return {
                status: 'success',
                message: 'Successfully rescheduled the queue',
                queueId: result.id,
                oldQueueId: rescheduleQueueDto.oldQueueId,
                serviceId: result.serviceId,
                uniqueSlotId: result.uniqueSlotId || Math.floor(1000 + Math.random() * 9000).toString(),
                timeSlot: result.timeSlot,
                date: result.date,
                hasSubUnits: result.hasSubUnits,
                subUnitId: result.subUnitId,
                subUnitName: result.subUnitName,
                position: result.position,
                timestamp: new Date().toISOString(),
            };
        }
        catch (error) {
            throw new common_1.HttpException({
                status: 'error',
                message: error.message || 'Failed to reschedule queue',
            }, common_1.HttpStatus.BAD_REQUEST);
        }
    }
    async getUserQueues(userId) {
        try {
            const queues = await this.customerService.getUserQueues(userId);
            return {
                status: 'success',
                queues,
            };
        }
        catch (error) {
            throw new common_1.HttpException({
                status: 'error',
                message: error.message || 'Failed to fetch user queues',
            }, common_1.HttpStatus.BAD_REQUEST);
        }
    }
    async getActiveUserQueues(userId) {
        try {
            const queues = await this.customerService.getActiveUserQueues(userId);
            return {
                status: 'success',
                queues,
            };
        }
        catch (error) {
            throw new common_1.HttpException({
                status: 'error',
                message: error.message || 'Failed to fetch active user queues',
            }, common_1.HttpStatus.BAD_REQUEST);
        }
    }
    async getQueuesByStatusQuery(userId, status) {
        try {
            console.log(`Fetching queues for userId: ${userId}, status: ${status}`);
            const queues = await this.customerService.getQueuesByStatus(userId, status);
            return {
                status: 'success',
                queues,
            };
        }
        catch (error) {
            console.error(`Error fetching queues by status: ${error.message}`);
            throw new common_1.HttpException({
                status: 'error',
                message: error.message || `Failed to fetch ${status} queues`,
            }, common_1.HttpStatus.BAD_REQUEST);
        }
    }
    async getQueuesByStatus(userId, status) {
        try {
            const queues = await this.customerService.getQueuesByStatus(userId, status);
            return {
                status: 'success',
                queues,
            };
        }
        catch (error) {
            throw new common_1.HttpException({
                status: 'error',
                message: error.message || `Failed to fetch ${status} queues`,
            }, common_1.HttpStatus.BAD_REQUEST);
        }
    }
    async getQueueById(queueId) {
        try {
            const queue = await this.customerService.getQueueById(queueId);
            return {
                status: 'success',
                queue,
            };
        }
        catch (error) {
            throw new common_1.HttpException({
                status: 'error',
                message: error.message || 'Failed to fetch queue details',
            }, common_1.HttpStatus.BAD_REQUEST);
        }
    }
    async getEstimatedWaitTime(queueId) {
        try {
            const queueIdNum = parseInt(queueId, 10);
            if (isNaN(queueIdNum)) {
                throw new common_1.BadRequestException('Invalid queue ID');
            }
            console.log(`Retrieving estimated wait time for queue: ${queueId}`);
            const waitTimeData = await this.queueFlowService.getEstimatedWaitTime(queueIdNum);
            return {
                status: 'success',
                data: {
                    queueId: queueIdNum,
                    waitTimeMinutes: waitTimeData.waitTimeMinutes,
                    waitTimeStatus: waitTimeData.waitTimeStatus,
                    estimatedServeTime: waitTimeData.estimatedServeTime.toISOString(),
                    position: waitTimeData.position,
                    initialPositionAtJoin: waitTimeData.initialPositionAtJoin
                }
            };
        }
        catch (error) {
            console.error(`Error getting estimated wait time for queue ${queueId}:`, error);
            throw new common_1.HttpException({
                status: 'error',
                message: error.message || 'Failed to get estimated wait time',
            }, common_1.HttpStatus.BAD_REQUEST);
        }
    }
    async getActualServiceTime(queueId) {
        try {
            const queueIdNum = parseInt(queueId, 10);
            if (isNaN(queueIdNum)) {
                throw new common_1.BadRequestException('Invalid queue ID');
            }
            console.log(`Retrieving actual service time for queue: ${queueId}`);
            const serviceTimeData = await this.queueFlowService.getActualServiceTime(queueIdNum);
            return {
                status: 'success',
                data: {
                    queueId: queueIdNum,
                    serviceTimeMinutes: serviceTimeData.serviceTimeMinutes,
                    servingStartedAt: serviceTimeData.servingStartedAt?.toISOString() || null,
                    statusUpdatedAt: serviceTimeData.statusUpdatedAt?.toISOString() || null,
                    status: serviceTimeData.status
                }
            };
        }
        catch (error) {
            console.error(`Error getting actual service time for queue ${queueId}:`, error);
            throw new common_1.HttpException({
                status: 'error',
                message: error.message || 'Failed to get actual service time',
            }, common_1.HttpStatus.BAD_REQUEST);
        }
    }
    async checkInQueue(queueId, body) {
        return this.customerService.checkInQueue(queueId, body.userId);
    }
    async toggleCheckInQueue(queueId, body) {
        return this.customerService.toggleCheckInQueue(queueId, body.userId);
    }
    async cancelQueue(queueId, body) {
        try {
            const queueIdNum = parseInt(queueId, 10);
            if (isNaN(queueIdNum) || queueIdNum <= 0) {
                throw new common_1.BadRequestException('Invalid queue ID format');
            }
            const queue = await this.customerService['queueRepository'].findOne({
                where: { id: queueIdNum, userId: body.userId },
                relations: ['service']
            });
            if (!queue) {
                throw new common_1.NotFoundException(`Queue with ID ${queueIdNum} not found for this user`);
            }
            const allQueues = await this.customerService['queueRepository'].find({
                where: {
                    serviceId: queue.serviceId,
                    date: queue.date,
                    timeSlot: queue.timeSlot,
                    status: (0, typeorm_1.In)(['waiting', 'serving']),
                },
                order: {
                    isVIP: 'DESC',
                    position: 'ASC',
                    createdAt: 'ASC'
                }
            });
            const servingQueues = allQueues.filter(q => q.status === 'serving');
            const waitingQueues = allQueues.filter(q => q.status === 'waiting');
            console.log(`Found ${servingQueues.length} serving queues and ${waitingQueues.length} waiting queues for service ${queue.serviceId}, date ${queue.date}, timeSlot ${queue.timeSlot}`);
            queue.status = 'cancelled';
            queue.statusUpdatedAt = new Date();
            queue.position = -1;
            const updatedQueue = await this.customerService['queueRepository'].save(queue);
            const redisService = this.customerService['redisService'];
            const queueData = await redisService.getQueue(queueIdNum.toString()) || {};
            queueData.status = 'cancelled';
            queueData.position = -1;
            await redisService.saveQueue(queueIdNum.toString(), queueData);
            await redisService.updateQueueStatus(queueIdNum.toString(), 'cancelled', true);
            const positions = {};
            let vipQueues = [];
            let normalQueues = [];
            let allRemainingQueues = [];
            if (queue.status === 'waiting' || queue.status === 'checked-in') {
                const remainingWaitingQueues = waitingQueues.filter(q => q.id !== queueIdNum);
                let position = 0;
                vipQueues = remainingWaitingQueues.filter(q => q.isVIP);
                normalQueues = remainingWaitingQueues.filter(q => !q.isVIP);
                console.log(`Reordering positions: ${vipQueues.length} VIP queues and ${normalQueues.length} normal queues`);
                for (const q of vipQueues) {
                    position++;
                    q.position = position;
                    positions[q.id] = position;
                    const qData = await redisService.getQueue(q.id.toString()) || {};
                    qData.position = position;
                    await redisService.saveQueue(q.id.toString(), qData);
                }
                for (const q of normalQueues) {
                    position++;
                    q.position = position;
                    positions[q.id] = position;
                    const qData = await redisService.getQueue(q.id.toString()) || {};
                    qData.position = position;
                    await redisService.saveQueue(q.id.toString(), qData);
                }
                allRemainingQueues = [...vipQueues, ...normalQueues];
                console.log(`Reordered positions for ${allRemainingQueues.length} queues after cancelling queue ${queueIdNum}`);
                if (allRemainingQueues.length > 0) {
                    await this.customerService['queueRepository'].save(allRemainingQueues);
                }
            }
            else {
                console.log(`Queue ${queueIdNum} was not in waiting status, no need to reorder positions`);
            }
            const dateStr = new Date(queue.date).toISOString().split('T')[0];
            await redisService.saveQueuePosition(queue.serviceId.toString(), dateStr, queue.timeSlot, positions);
            await redisService.invalidateQueuePosition(queue.serviceId.toString(), dateStr, queue.timeSlot);
            await redisService.invalidateServiceQueues(queue.serviceId.toString(), dateStr);
            await redisService.invalidateServiceQueues(queue.serviceId.toString(), 'all');
            try {
                console.log(`Recalculating estimated serve times after queue ${queueId} was cancelled`);
                await this.queueFlowService.recalculateEstimatedServeTimes(queue.serviceId, queue.date, queue.timeSlot);
                console.log(`Successfully recalculated estimated serve times for all queues after cancelling queue ${queueId}`);
            }
            catch (error) {
                console.error(`Error recalculating estimated serve times: ${error.message}`);
            }
            return {
                status: 'success',
                message: 'Queue cancelled and positions reordered',
                data: updatedQueue
            };
        }
        catch (error) {
            console.error('Error cancelling queue:', error);
            throw new common_1.HttpException({
                status: 'error',
                message: error.message || 'Failed to cancel queue',
            }, common_1.HttpStatus.BAD_REQUEST);
        }
    }
    async getServiceActiveQueues(serviceId, date) {
        try {
            const queues = await this.customerService.getServiceActiveQueues(parseInt(serviceId), date);
            return {
                status: 'success',
                queues,
            };
        }
        catch (error) {
            throw new common_1.HttpException({
                status: 'error',
                message: error.message || 'Failed to fetch service active queues',
            }, common_1.HttpStatus.BAD_REQUEST);
        }
    }
    async completeQueue(queueId, body) {
        try {
            console.log(`Complete queue request for queue ${queueId} with isCheckedIn=${body.isCheckedIn}`);
            const queueIdNum = parseInt(queueId, 10);
            if (isNaN(queueIdNum) || queueIdNum <= 0) {
                throw new common_1.BadRequestException('Invalid queue ID format');
            }
            const queue = await this.customerService['queueRepository'].findOne({
                where: { id: queueIdNum, userId: body.userId },
                relations: ['service']
            });
            if (!queue) {
                throw new common_1.NotFoundException(`Queue with ID ${queueIdNum} not found for this user`);
            }
            const allQueues = await this.customerService['queueRepository'].find({
                where: {
                    serviceId: queue.serviceId,
                    date: queue.date,
                    timeSlot: queue.timeSlot,
                    status: (0, typeorm_1.In)(['waiting', 'serving']),
                },
                order: {
                    isVIP: 'DESC',
                    position: 'ASC',
                    createdAt: 'ASC'
                }
            });
            const servingQueues = allQueues.filter(q => q.status === 'serving');
            const waitingQueues = allQueues.filter(q => q.status === 'waiting');
            console.log(`Found ${servingQueues.length} serving queues and ${waitingQueues.length} waiting queues for service ${queue.serviceId}, date ${queue.date}, timeSlot ${queue.timeSlot}`);
            queue.status = 'completed';
            queue.statusUpdatedAt = new Date();
            queue.position = -1;
            if (body.isCheckedIn !== undefined) {
                queue.isCheckedIn = body.isCheckedIn;
            }
            const updatedQueue = await this.customerService['queueRepository'].save(queue);
            const redisService = this.customerService['redisService'];
            const queueData = await redisService.getQueue(queueIdNum.toString()) || {};
            queueData.status = 'completed';
            queueData.position = -1;
            await redisService.saveQueue(queueIdNum.toString(), queueData);
            await redisService.updateQueueStatus(queueIdNum.toString(), 'completed', true);
            const positions = {};
            let vipQueues = [];
            let normalQueues = [];
            let allRemainingQueues = [];
            if (queue.status === 'waiting' || queue.status === 'checked-in') {
                const remainingWaitingQueues = waitingQueues.filter(q => q.id !== queueIdNum);
                let position = 0;
                vipQueues = remainingWaitingQueues.filter(q => q.isVIP);
                normalQueues = remainingWaitingQueues.filter(q => !q.isVIP);
                console.log(`Reordering positions: ${vipQueues.length} VIP queues and ${normalQueues.length} normal queues`);
                for (const q of vipQueues) {
                    position++;
                    q.position = position;
                    positions[q.id] = position;
                    const qData = await redisService.getQueue(q.id.toString()) || {};
                    qData.position = position;
                    await redisService.saveQueue(q.id.toString(), qData);
                }
                for (const q of normalQueues) {
                    position++;
                    q.position = position;
                    positions[q.id] = position;
                    const qData = await redisService.getQueue(q.id.toString()) || {};
                    qData.position = position;
                    await redisService.saveQueue(q.id.toString(), qData);
                }
                allRemainingQueues = [...vipQueues, ...normalQueues];
                console.log(`Reordered positions for ${allRemainingQueues.length} queues after completing queue ${queueIdNum}`);
                if (allRemainingQueues.length > 0) {
                    await this.customerService['queueRepository'].save(allRemainingQueues);
                }
            }
            else {
                console.log(`Queue ${queueIdNum} was not in waiting status, no need to reorder positions`);
            }
            const dateStr = new Date(queue.date).toISOString().split('T')[0];
            await redisService.saveQueuePosition(queue.serviceId.toString(), dateStr, queue.timeSlot, positions);
            await redisService.invalidateQueuePosition(queue.serviceId.toString(), dateStr, queue.timeSlot);
            await redisService.invalidateServiceQueues(queue.serviceId.toString(), dateStr);
            await redisService.invalidateServiceQueues(queue.serviceId.toString(), 'all');
            try {
                console.log(`Recalculating estimated serve times after queue ${queueId} was completed`);
                this.queueFlowService.recalculateEstimatedServeTimes(queue.serviceId, queue.date, queue.timeSlot).catch(error => {
                    console.error(`Error recalculating estimated serve times: ${error.message}`);
                });
            }
            catch (error) {
                console.error(`Error triggering recalculation of estimated serve times: ${error.message}`);
            }
            return {
                status: 'success',
                message: 'Queue completed and positions reordered',
                data: updatedQueue
            };
        }
        catch (error) {
            console.error('Error completing queue:', error);
            throw new common_1.HttpException({
                status: 'error',
                message: error.message || 'Failed to complete queue',
            }, common_1.HttpStatus.BAD_REQUEST);
        }
    }
    async getServiceQueueCounts(serviceId, date, subUnitId) {
        try {
            const counts = await this.customerService.getServiceQueueCounts(parseInt(serviceId), date, subUnitId);
            return {
                status: 'success',
                counts
            };
        }
        catch (error) {
            throw new common_1.HttpException({
                status: 'error',
                message: error.message || 'Failed to fetch queue counts',
            }, common_1.HttpStatus.BAD_REQUEST);
        }
    }
    async getServiceQueuesFromRedis(serviceId, subUnitId) {
        try {
            const queues = await this.customerService.getServiceActiveQueues(parseInt(serviceId), 'all');
            console.log(`Got ${queues.length} queues for service ${serviceId} from Redis/DB`);
            let filteredQueues = queues;
            if (subUnitId !== undefined) {
                console.log(`Filtering queues by subUnitId: ${subUnitId}`);
                filteredQueues = queues.filter(queue => queue.hasSubUnits && queue.subUnitId === subUnitId);
                console.log(`After filtering, found ${filteredQueues.length} queues for subUnitId ${subUnitId}`);
            }
            return {
                status: 'success',
                queues: filteredQueues,
            };
        }
        catch (error) {
            throw new common_1.HttpException({
                status: 'error',
                message: error.message || 'Failed to fetch queues from Redis',
            }, common_1.HttpStatus.BAD_REQUEST);
        }
    }
    async getServiceQueuesAllStatuses(serviceId) {
        try {
            console.log(`API request received to fetch all queues for service ID: ${serviceId}`);
            const id = parseInt(serviceId);
            if (isNaN(id)) {
                console.error(`Invalid service ID format: ${serviceId}`);
                throw new Error('Invalid service ID format');
            }
            const startTime = Date.now();
            const allQueues = await this.customerService.getAllQueuesDirectFromRedis(id);
            const endTime = Date.now();
            console.log(`Fast query completed in ${endTime - startTime}ms for service ${id} (found ${allQueues.length} queues)`);
            return {
                status: 'success',
                queues: allQueues,
            };
        }
        catch (error) {
            console.error(`Error in getServiceQueuesAllStatuses for service ${serviceId}:`, error);
            throw new common_1.HttpException({
                status: 'error',
                message: error.message || 'Failed to fetch all queues',
            }, common_1.HttpStatus.BAD_REQUEST);
        }
    }
    async getServiceQueuesFromRedisByStatus(serviceId, status, subUnitId) {
        try {
            if (status === 'all') {
                throw new common_1.HttpException({
                    status: 'error',
                    message: 'For all statuses, use the /all endpoint directly',
                }, common_1.HttpStatus.BAD_REQUEST);
            }
            const mappedStatus = status === 'up' ? 'upcoming' : status;
            const validStatuses = ['upcoming', 'completed', 'cancelled', 'waiting', 'checked-in'];
            if (!validStatuses.includes(mappedStatus)) {
                throw new common_1.HttpException({
                    status: 'error',
                    message: 'Invalid status parameter. Must be one of: upcoming, completed, cancelled',
                }, common_1.HttpStatus.BAD_REQUEST);
            }
            let queues = await this.customerService.getServiceActiveQueues(parseInt(serviceId), 'all');
            console.log(`Got ${queues.length} queues for service ${serviceId} with status ${mappedStatus}`);
            if (mappedStatus !== 'upcoming' && mappedStatus !== 'waiting') {
                const statusQueues = await this.customerService.getServiceQueuesByStatus(parseInt(serviceId), 'all', mappedStatus);
                queues = statusQueues;
            }
            if (subUnitId !== undefined) {
                console.log(`Filtering queues by subUnitId: ${subUnitId}`);
                queues = queues.filter(queue => queue.hasSubUnits && queue.subUnitId === subUnitId);
                console.log(`After filtering, found ${queues.length} queues for subUnitId ${subUnitId}`);
            }
            return {
                status: 'success',
                queues,
            };
        }
        catch (error) {
            throw new common_1.HttpException({
                status: 'error',
                message: error.message || `Failed to fetch ${status} queues from Redis`,
            }, common_1.HttpStatus.BAD_REQUEST);
        }
    }
    async updateExpiredQueues() {
        console.log('Manual update of expired queues triggered');
        return this.customerService.updateExpiredQueues();
    }
    async getUserProfileByClerkId(clerkId) {
        try {
            const user = await this.customerService.findUserByClerkId(clerkId);
            if (!user) {
                throw new common_1.NotFoundException('User not found for this clerk ID');
            }
            return {
                status: 'success',
                user: {
                    id: user.id,
                    email: user.email,
                    fullName: user.fullName,
                    mobileNumber: user.mobileNumber,
                    isVIP: user.isVIP,
                    lastLocationUpdate: user.lastLocationUpdate,
                    clerkId: user.clerkId
                }
            };
        }
        catch (error) {
            throw new common_1.HttpException({
                status: 'error',
                message: error.message || 'Failed to fetch user profile',
            }, common_1.HttpStatus.BAD_REQUEST);
        }
    }
    async getUserMobileByClerkId(clerkId) {
        try {
            const user = await this.customerService.findUserByClerkId(clerkId);
            if (!user) {
                throw new common_1.NotFoundException('User not found for this clerk ID');
            }
            return {
                status: 'success',
                data: {
                    mobileNumber: user.mobileNumber || null,
                    fullName: user.fullName || null,
                    email: user.email
                }
            };
        }
        catch (error) {
            throw new common_1.HttpException({
                status: 'error',
                message: error.message || 'Failed to fetch user mobile number',
            }, common_1.HttpStatus.BAD_REQUEST);
        }
    }
    async getQueueMobileNumber(queueId) {
        try {
            const queue = await this.customerService.getQueueById(queueId);
            if (!queue) {
                throw new common_1.NotFoundException(`Queue with ID ${queueId} not found`);
            }
            if (queue.mobileNumber) {
                return {
                    status: 'success',
                    data: {
                        mobileNumber: queue.mobileNumber,
                        fullName: queue.fullName || queue.userName || 'Unknown',
                        userId: queue.userId
                    }
                };
            }
            if (queue.userId && !queue.userId.includes('@')) {
                const user = await this.customerService.findUserByClerkId(queue.userId);
                if (user && user.mobileNumber) {
                    return {
                        status: 'success',
                        data: {
                            mobileNumber: user.mobileNumber,
                            fullName: user.fullName || queue.fullName || queue.userName || 'Unknown',
                            userId: queue.userId
                        }
                    };
                }
            }
            return {
                status: 'success',
                data: {
                    mobileNumber: null,
                    fullName: queue.fullName || queue.userName || 'Unknown',
                    userId: queue.userId
                }
            };
        }
        catch (error) {
            throw new common_1.HttpException({
                status: 'error',
                message: error.message || 'Failed to fetch queue mobile number',
            }, common_1.HttpStatus.BAD_REQUEST);
        }
    }
    async getQueueByUniqueSlotId(uniqueSlotId) {
        try {
            const queue = await this.customerService.getQueueByUniqueSlotId(uniqueSlotId);
            return {
                status: 'success',
                queue,
            };
        }
        catch (error) {
            throw new common_1.HttpException({
                status: 'error',
                message: error.message || 'Failed to fetch queue by unique slot ID',
            }, common_1.HttpStatus.BAD_REQUEST);
        }
    }
    async ensureQueueConsistency(serviceId) {
        try {
            console.log(`Ensuring queue consistency for service ${serviceId}`);
            const id = parseInt(serviceId);
            if (isNaN(id)) {
                throw new common_1.BadRequestException('Invalid service ID format');
            }
            await this.customerService.ensureQueueConsistency(id);
            await this.customerService.restoreQueueData(id);
            return {
                status: 'success',
                message: 'Queue data consistency check completed'
            };
        }
        catch (error) {
            console.error('Error ensuring queue consistency:', error);
            throw new common_1.HttpException({
                status: 'error',
                message: error.message || 'Failed to ensure queue consistency',
            }, common_1.HttpStatus.BAD_REQUEST);
        }
    }
    async getGracePeriodStatus(queueId) {
        try {
            const queueData = await this.customerService.getQueueGracePeriodStatus(parseInt(queueId, 10));
            return { status: 'success', data: queueData };
        }
        catch (error) {
            return { status: 'error', message: error.message };
        }
    }
    async markGracePeriodExpired(queueId) {
        try {
            await this.schedulerService.checkAndMarkExpiredGracePeriod(parseInt(queueId, 10));
            return { status: 'success', message: 'Grace period check completed' };
        }
        catch (error) {
            return { status: 'error', message: error.message };
        }
    }
    async confirmPresence(queueId, body) {
        try {
            const queue = await this.customerService.getQueueById(parseInt(queueId, 10), body.userId);
            if (!queue) {
                throw new common_1.NotFoundException('Queue not found or does not belong to this user');
            }
            const updatedQueue = await this.queueFlowService.handlePresenceConfirmation(parseInt(queueId, 10), body.isPresent);
            return {
                status: 'success',
                message: body.isPresent ? 'Presence confirmed' : 'Marked as not present',
                data: updatedQueue
            };
        }
        catch (error) {
            throw new common_1.HttpException({
                status: 'error',
                message: error.message || 'Failed to confirm presence'
            }, error.status || common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async getServingStatus(queueId) {
        try {
            const queueIdNum = parseInt(queueId, 10);
            if (isNaN(queueIdNum)) {
                throw new common_1.BadRequestException('Invalid queue ID');
            }
            console.log(`Retrieving serving status for queue: ${queueId}`);
            try {
                const redisData = await this.customerService['redisService'].getQueue(queueId);
                if (redisData &&
                    redisData.status === 'serving' &&
                    redisData.currentlyServing === true &&
                    redisData.servingStartedAt) {
                    console.log(`Found cached serving data in Redis for queue ${queueId}`);
                    const serviceSetup = await this.customerService['serviceSetupRepository'].findOne({
                        where: { service: { id: redisData.serviceId } }
                    });
                    let servingTimeMinutes = 15;
                    if (redisData.hasSubUnits && redisData.subUnitId && serviceSetup?.setupData?.hasSubUnits &&
                        Array.isArray(serviceSetup.setupData.subUnits) && serviceSetup.setupData.subUnits.length > 0) {
                        const subUnitId = parseInt(redisData.subUnitId, 10);
                        const subUnitIndex = isNaN(subUnitId) ? 0 : Math.min(subUnitId, serviceSetup.setupData.subUnits.length - 1);
                        const subUnit = serviceSetup.setupData.subUnits[subUnitIndex];
                        if (subUnit?.avgServeTime) {
                            servingTimeMinutes = parseInt(subUnit.avgServeTime, 10);
                            console.log(`Using subunit ${subUnitIndex} serve time: ${servingTimeMinutes} minutes`);
                        }
                    }
                    else if (serviceSetup?.setupData?.servingTime) {
                        servingTimeMinutes = parseInt(serviceSetup.setupData.servingTime, 10);
                        console.log(`Using service setup serve time: ${servingTimeMinutes} minutes`);
                    }
                    if (redisData.serveTime) {
                        servingTimeMinutes = redisData.serveTime;
                        console.log(`Using queue's stored serve time from Redis: ${servingTimeMinutes} minutes`);
                    }
                    else if (redisData.servingTimeMinutes) {
                        servingTimeMinutes = redisData.servingTimeMinutes;
                        console.log(`Using servingTimeMinutes from Redis: ${servingTimeMinutes} minutes`);
                    }
                    const servingStartedAt = new Date(redisData.servingStartedAt);
                    const estimatedEndTime = new Date(servingStartedAt);
                    estimatedEndTime.setMinutes(estimatedEndTime.getMinutes() + servingTimeMinutes);
                    const now = new Date();
                    const remainingMs = Math.max(0, estimatedEndTime.getTime() - now.getTime());
                    const remainingSeconds = Math.floor(remainingMs / 1000);
                    const remainingMinutes = Math.floor(remainingSeconds / 60);
                    console.log(`Queue ${queueId} serving time calculation from Redis:`);
                    console.log(`Started: ${servingStartedAt.toISOString()}`);
                    console.log(`End time: ${estimatedEndTime.toISOString()}`);
                    console.log(`Serving time: ${servingTimeMinutes} minutes`);
                    console.log(`Remaining: ${remainingMinutes} minutes ${remainingSeconds % 60} seconds`);
                    return {
                        status: 'success',
                        data: {
                            queueId: redisData.id,
                            serviceId: redisData.serviceId,
                            isServing: true,
                            servingStartedAt: redisData.servingStartedAt,
                            estimatedEndTime: estimatedEndTime.toISOString(),
                            servingTimeMinutes: servingTimeMinutes,
                            remainingMinutes: remainingMinutes,
                            remainingSeconds: remainingSeconds
                        }
                    };
                }
            }
            catch (error) {
                console.error(`Error getting Redis serving data for queue ${queueId}:`, error);
            }
            const queue = await this.customerService['queueRepository'].findOne({
                where: { id: queueIdNum },
                relations: ['service']
            });
            if (!queue) {
                throw new common_1.NotFoundException(`Queue with ID ${queueIdNum} not found`);
            }
            const serviceSetup = await this.customerService['serviceSetupRepository'].findOne({
                where: { service: { id: queue.serviceId } }
            });
            const servingTimeMinutes = serviceSetup?.setupData?.servingTime
                ? parseInt(serviceSetup.setupData.servingTime)
                : 15;
            const isServing = queue.status === 'serving' && queue.currentlyServing;
            if (!isServing || !queue.servingStartedAt) {
                return {
                    status: 'success',
                    data: {
                        queueId: queue.id,
                        serviceId: queue.serviceId,
                        isServing: false,
                        servingStartedAt: null,
                        estimatedEndTime: null,
                        servingTimeMinutes: servingTimeMinutes,
                        remainingMinutes: 0,
                        remainingSeconds: 0
                    }
                };
            }
            const servingStartedAt = new Date(queue.servingStartedAt);
            const estimatedEndTime = new Date(servingStartedAt);
            estimatedEndTime.setMinutes(estimatedEndTime.getMinutes() + servingTimeMinutes);
            const now = new Date();
            const remainingMs = Math.max(0, estimatedEndTime.getTime() - now.getTime());
            const remainingSeconds = Math.floor(remainingMs / 1000);
            const remainingMinutes = Math.floor(remainingSeconds / 60);
            console.log(`Queue ${queueId} serving time calculation from DB:`);
            console.log(`Started: ${servingStartedAt.toISOString()}`);
            console.log(`End time: ${estimatedEndTime.toISOString()}`);
            console.log(`Serving time: ${servingTimeMinutes} minutes`);
            console.log(`Remaining: ${remainingMinutes} minutes ${remainingSeconds % 60} seconds`);
            try {
                const redisData = {
                    id: queue.id,
                    serviceId: queue.serviceId,
                    status: 'serving',
                    currentlyServing: true,
                    servingStartedAt: queue.servingStartedAt.toISOString(),
                    estimatedEndTime: estimatedEndTime.toISOString(),
                    servingTime: servingTimeMinutes,
                    servingTimeMinutes: servingTimeMinutes,
                    remainingMinutes: remainingMinutes,
                    remainingSeconds: remainingSeconds,
                    statusUpdatedAt: new Date().toISOString(),
                    calculatedAt: new Date().toISOString()
                };
                await this.customerService['redisService'].set(`queue:${queueId}`, redisData, { ex: 86400 });
            }
            catch (error) {
                console.error(`Error updating Redis serving data for queue ${queueId}:`, error);
            }
            return {
                status: 'success',
                data: {
                    queueId: queue.id,
                    serviceId: queue.serviceId,
                    isServing: true,
                    servingStartedAt: queue.servingStartedAt,
                    estimatedEndTime: estimatedEndTime.toISOString(),
                    servingTimeMinutes: servingTimeMinutes,
                    remainingMinutes: remainingMinutes,
                    remainingSeconds: remainingSeconds
                }
            };
        }
        catch (error) {
            throw new common_1.HttpException({
                status: 'error',
                message: error.message || 'Failed to get serving status'
            }, error.status || common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async getServingQueues(userId) {
        try {
            console.log(`Fetching serving queues for userId: ${userId}`);
            const queues = await this.customerService.getQueuesByStatus(userId, 'serving');
            return {
                status: 'success',
                queues,
            };
        }
        catch (error) {
            console.error(`Error fetching serving queues: ${error.message}`);
            throw new common_1.HttpException({
                status: 'error',
                message: error.message || 'Failed to fetch serving queues',
            }, common_1.HttpStatus.BAD_REQUEST);
        }
    }
    async getQueuesProgressive(userId, section, offset, limit) {
        try {
            console.log(`Fetching progressive queues for userId: ${userId}, section: ${section}, offset: ${offset}, limit: ${limit}`);
            if (!['upcoming', 'completed', 'cancelled'].includes(section)) {
                throw new common_1.HttpException({
                    status: 'error',
                    message: 'Invalid section parameter. Must be one of: upcoming, completed, cancelled',
                }, common_1.HttpStatus.BAD_REQUEST);
            }
            const offsetNum = parseInt(offset || '0', 10);
            const limitNum = parseInt(limit || '1', 10);
            const result = await this.customerService.getQueuesProgressive(userId, section, offsetNum, limitNum);
            return {
                status: 'success',
                ...result,
            };
        }
        catch (error) {
            console.error(`Error fetching progressive queues: ${error.message}`);
            throw new common_1.HttpException({
                status: 'error',
                message: error.message || 'Failed to fetch progressive queues',
            }, common_1.HttpStatus.BAD_REQUEST);
        }
    }
};
exports.CustomerController = CustomerController;
__decorate([
    (0, common_1.Post)('register'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], CustomerController.prototype, "registerUser", null);
__decorate([
    (0, common_1.Post)('update-location'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], CustomerController.prototype, "updateLocation", null);
__decorate([
    (0, common_1.Put)('update-vip'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], CustomerController.prototype, "updateVIPStatus", null);
__decorate([
    (0, common_1.Get)('profile/:email'),
    __param(0, (0, common_1.Param)('email')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], CustomerController.prototype, "getUserProfile", null);
__decorate([
    (0, common_1.Put)('update-details'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], CustomerController.prototype, "updateUserDetails", null);
__decorate([
    (0, common_1.Put)('update-clerk-id'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], CustomerController.prototype, "updateClerkId", null);
__decorate([
    (0, common_1.Post)('wishlist/add'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], CustomerController.prototype, "addToWishlist", null);
__decorate([
    (0, common_1.Delete)('wishlist/remove'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], CustomerController.prototype, "removeFromWishlist", null);
__decorate([
    (0, common_1.Get)('wishlist/:email'),
    __param(0, (0, common_1.Param)('email')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], CustomerController.prototype, "getWishlist", null);
__decorate([
    (0, common_1.Post)('queues/join'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [join_queue_dto_1.JoinQueueDto]),
    __metadata("design:returntype", Promise)
], CustomerController.prototype, "joinQueue", null);
__decorate([
    (0, common_1.Post)('queues/reschedule'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [reschedule_queue_dto_1.RescheduleQueueDto]),
    __metadata("design:returntype", Promise)
], CustomerController.prototype, "rescheduleQueue", null);
__decorate([
    (0, common_1.Get)('queues/:userId'),
    __param(0, (0, common_1.Param)('userId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], CustomerController.prototype, "getUserQueues", null);
__decorate([
    (0, common_1.Get)('queues/:userId/active'),
    __param(0, (0, common_1.Param)('userId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], CustomerController.prototype, "getActiveUserQueues", null);
__decorate([
    (0, common_1.Get)('queues/:userId/by-status'),
    __param(0, (0, common_1.Param)('userId')),
    __param(1, (0, common_1.Query)('status')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], CustomerController.prototype, "getQueuesByStatusQuery", null);
__decorate([
    (0, common_1.Get)('queues/:userId/:status'),
    __param(0, (0, common_1.Param)('userId')),
    __param(1, (0, common_1.Param)('status')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], CustomerController.prototype, "getQueuesByStatus", null);
__decorate([
    (0, common_1.Get)('queue/:queueId'),
    __param(0, (0, common_1.Param)('queueId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], CustomerController.prototype, "getQueueById", null);
__decorate([
    (0, common_1.Get)('queue/:queueId/estimated-wait-time'),
    __param(0, (0, common_1.Param)('queueId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], CustomerController.prototype, "getEstimatedWaitTime", null);
__decorate([
    (0, common_1.Get)('queue/:queueId/actual-service-time'),
    __param(0, (0, common_1.Param)('queueId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], CustomerController.prototype, "getActualServiceTime", null);
__decorate([
    (0, common_1.Put)('/queue/:queueId/check-in'),
    __param(0, (0, common_1.Param)('queueId')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], CustomerController.prototype, "checkInQueue", null);
__decorate([
    (0, common_1.Put)('/queue/:queueId/toggle-check-in'),
    __param(0, (0, common_1.Param)('queueId')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], CustomerController.prototype, "toggleCheckInQueue", null);
__decorate([
    (0, common_1.Put)('queue/:queueId/cancel'),
    __param(0, (0, common_1.Param)('queueId')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], CustomerController.prototype, "cancelQueue", null);
__decorate([
    (0, common_1.Get)('queues/active/:serviceId/:date'),
    __param(0, (0, common_1.Param)('serviceId')),
    __param(1, (0, common_1.Param)('date')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], CustomerController.prototype, "getServiceActiveQueues", null);
__decorate([
    (0, common_1.Put)('queue/:queueId/complete'),
    __param(0, (0, common_1.Param)('queueId')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], CustomerController.prototype, "completeQueue", null);
__decorate([
    (0, common_1.Get)('queues/counts/:serviceId/:date'),
    __param(0, (0, common_1.Param)('serviceId')),
    __param(1, (0, common_1.Param)('date')),
    __param(2, (0, common_1.Query)('subUnitId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, String]),
    __metadata("design:returntype", Promise)
], CustomerController.prototype, "getServiceQueueCounts", null);
__decorate([
    (0, common_1.Get)('queues/redis/:serviceId'),
    __param(0, (0, common_1.Param)('serviceId')),
    __param(1, (0, common_1.Query)('subUnitId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], CustomerController.prototype, "getServiceQueuesFromRedis", null);
__decorate([
    (0, common_1.Get)('queues/redis/:serviceId/all'),
    __param(0, (0, common_1.Param)('serviceId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], CustomerController.prototype, "getServiceQueuesAllStatuses", null);
__decorate([
    (0, common_1.Get)('queues/redis/:serviceId/:status'),
    __param(0, (0, common_1.Param)('serviceId')),
    __param(1, (0, common_1.Param)('status')),
    __param(2, (0, common_1.Query)('subUnitId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, String]),
    __metadata("design:returntype", Promise)
], CustomerController.prototype, "getServiceQueuesFromRedisByStatus", null);
__decorate([
    (0, common_1.Get)('update-expired-queues'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], CustomerController.prototype, "updateExpiredQueues", null);
__decorate([
    (0, common_1.Get)('profile-by-clerk/:clerkId'),
    __param(0, (0, common_1.Param)('clerkId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], CustomerController.prototype, "getUserProfileByClerkId", null);
__decorate([
    (0, common_1.Get)('user-mobile/:clerkId'),
    __param(0, (0, common_1.Param)('clerkId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], CustomerController.prototype, "getUserMobileByClerkId", null);
__decorate([
    (0, common_1.Get)('queue-mobile/:queueId'),
    __param(0, (0, common_1.Param)('queueId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], CustomerController.prototype, "getQueueMobileNumber", null);
__decorate([
    (0, common_1.Get)('queue-by-uniqueslotid/:uniqueSlotId'),
    __param(0, (0, common_1.Param)('uniqueSlotId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], CustomerController.prototype, "getQueueByUniqueSlotId", null);
__decorate([
    (0, common_1.Get)('queues/ensure-consistency/:serviceId'),
    __param(0, (0, common_1.Param)('serviceId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], CustomerController.prototype, "ensureQueueConsistency", null);
__decorate([
    (0, common_1.Get)('queue/:queueId/grace-period-status'),
    __param(0, (0, common_1.Param)('queueId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], CustomerController.prototype, "getGracePeriodStatus", null);
__decorate([
    (0, common_1.Put)('queue/:queueId/grace-period-expired'),
    __param(0, (0, common_1.Param)('queueId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], CustomerController.prototype, "markGracePeriodExpired", null);
__decorate([
    (0, common_1.Put)('queue/:queueId/confirm-presence'),
    __param(0, (0, common_1.Param)('queueId')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], CustomerController.prototype, "confirmPresence", null);
__decorate([
    (0, common_1.Get)('queue/:queueId/serving-status'),
    __param(0, (0, common_1.Param)('queueId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], CustomerController.prototype, "getServingStatus", null);
__decorate([
    (0, common_1.Get)('queues/:userId/status/serving'),
    __param(0, (0, common_1.Param)('userId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], CustomerController.prototype, "getServingQueues", null);
__decorate([
    (0, common_1.Get)('queues/:userId/progressive/:section'),
    __param(0, (0, common_1.Param)('userId')),
    __param(1, (0, common_1.Param)('section')),
    __param(2, (0, common_1.Query)('offset')),
    __param(3, (0, common_1.Query)('limit')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, String, String]),
    __metadata("design:returntype", Promise)
], CustomerController.prototype, "getQueuesProgressive", null);
exports.CustomerController = CustomerController = __decorate([
    (0, common_1.Controller)('customer'),
    __metadata("design:paramtypes", [customer_service_1.CustomerService,
        queue_flow_service_1.QueueFlowService,
        scheduler_service_1.SchedulerService])
], CustomerController);
//# sourceMappingURL=customer.controller.js.map