import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  TextInput,
  SafeAreaView,
  TouchableOpacity,
  Image,
  StatusBar,
  Alert,
  Modal,
  ActivityIndicator,
} from "react-native";
import { useRouter } from "expo-router";
import { useUser } from "@clerk/clerk-expo";
import ButtonBlueMain from "@/components/ButtonBlueMain";
import { images } from "@/constants";

export default function BankDetailsScreen() {
  const router = useRouter();
  const { user } = useUser();
  const [isSaving, setIsSaving] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [formData, setFormData] = useState({
    accountNumber: "",
    accountHolderName: "",
    ifscCode: "",
  });

  useEffect(() => {
    loadExistingBankDetails();
  }, []);

  const loadExistingBankDetails = async () => {
    try {
      setIsLoading(true);
      if (!user?.primaryEmailAddress?.emailAddress) return;

      const email = encodeURIComponent(user.primaryEmailAddress.emailAddress);
      const serviceResponse = await fetch(
        `http://***************:3000/api/partner/service-details/${email}`
      );

      if (!serviceResponse.ok) {
        throw new Error('Failed to fetch service details');
      }

      const serviceData = await serviceResponse.json();
      if (!serviceData.id) return;

      // Try to load bank details
      const bankResponse = await fetch(
        `http://***************:3000/api/partner/bank-details/${serviceData.id}`
      );

      if (bankResponse.ok) {
        const bankData = await bankResponse.json();
        if (bankData.data) {
          setFormData(bankData.data);
        }
      }
    } catch (error) {
      console.error('Error loading bank details:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSave = async () => {
    if (!validateForm()) return;
    try {
      setIsSaving(true);
      setIsLoading(true);
      const email = user?.primaryEmailAddress?.emailAddress;
      if (!email) throw new Error('Email not found');

      // Get service ID
      const serviceResponse = await fetch(
        `http://***************:3000/api/partner/service-details/${encodeURIComponent(email)}`
      );
      const serviceData = await serviceResponse.json();
      if (!serviceData.id) throw new Error('Service not found');

      // Save bank details
      const response = await fetch(
        `http://***************:3000/api/partner/bank-details/${serviceData.id}`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(formData),
        }
      );

      if (!response.ok) throw new Error('Failed to save bank details');

      // Re-check required setups in dashboard before navigating back
      const dashboardResponse = await fetch(
        `http://***************:3000/api/partner/bank-details/${serviceData.id}`
      );
      router.back();

    } catch (error) {
      console.error(error);
      Alert.alert('Error', 'Failed to save bank details');
    } finally {
      setIsSaving(false);
      setIsLoading(false);
    }
  };

  const validateForm = () => {
    if (!formData.accountNumber || formData.accountNumber.length < 9) {
      Alert.alert('Error', 'Please enter a valid account number');
      return false;
    }
    if (!formData.accountHolderName) {
      Alert.alert('Error', 'Please enter account holder name');
      return false;
    }
    if (!formData.ifscCode || formData.ifscCode.length !== 11) {
      Alert.alert('Error', 'Please enter a valid IFSC code');
      return false;
    }
    return true;
  };

  return (
    <SafeAreaView className="flex-1 bg-white">
      <StatusBar barStyle="dark-content" backgroundColor="white" />
      
      {isLoading && (
        <View className="absolute w-full h-full bg-black/30 z-50 items-center justify-center">
          <View className="bg-white p-8 rounded-xl items-center">
            <ActivityIndicator size="large" color="#159AFF" className="mb-2" />
            <Text className="font-poppins-medium text-secondary-500 text-base">Loading...</Text>
          </View>
        </View>
      )}
      
      <View className="px-8 pt-8 ">
        <View className="w-full flex-row">
          <TouchableOpacity
            className="flex-row items-center absolute left-0 border border-secondary-600 w-12 h-12 rounded-full justify-center"
            onPress={() => router.back()}
          >
            <Image source={images.back} className="w-4 h-4" />
          </TouchableOpacity>
          <View className="flex-1 items-center mt-2 mb-8">
            <Text className="font-poppins-medium text-xl">Bank Details</Text>
          </View>
        </View>
      </View>

      {/* Info Box */}
      <View className="mx-10 my-4 bg-primary-500/10 border border-primary-500/30 p-4 rounded-xl flex-row items-start mb-6">
        <Image 
          source={images.secure} 
          className="w-[25.5] h-8 mr-3 mt-0.5" 
          tintColor="#159AFF"
        />
        <View className="flex-1">
          <Text className="text-sm font-poppins-medium text-primary-500 mb-1">
            Secure Banking
          </Text>
          <Text className="text-xs text-secondary-600">
            Your banking information is encrypted and secure.
          </Text>
        </View>
      </View>

      <View className="px-10">
        <Text className="text-sm mb-1 text-secondary-600 font-[Poppins-Medium]">
          Account Number
        </Text>
        <TextInput
          className="border border-gray-300 rounded-xl p-5 mt-2 mb-6"
          value={formData.accountNumber}
          placeholder="Enter  account number"
          placeholderTextColor="#9CA3AF"
          onChangeText={(text) => setFormData({ ...formData, accountNumber: text })}
          keyboardType="numeric"
          maxLength={18}
        />

        <Text className="text-sm mb-1 text-secondary-600 font-[Poppins-Medium]">
          Account Holder Name
        </Text>
        <TextInput
          className="border border-gray-300 rounded-xl p-5 mt-2 mb-6"
          value={formData.accountHolderName}
          placeholder="Enter account holder name"
          placeholderTextColor="#9CA3AF"
          onChangeText={(text) => setFormData({ ...formData, accountHolderName: text })}
        />


        <Text className="text-sm mb-1 text-secondary-600 font-[Poppins-Medium]">
          IFSC Code
        </Text>
        <TextInput
          className="border border-gray-300 rounded-xl p-5 mt-2 mb-6"
          value={formData.ifscCode}
          placeholder="Enter IFSC Code"
          placeholderTextColor="#9CA3AF"
          onChangeText={(text) => setFormData({ ...formData, ifscCode: text.toUpperCase() })}
          autoCapitalize="characters"
          maxLength={11}
        />
      </View>

      <View className="p-4 border-t absolute bottom-0 w-full justify-center items-center border-gray-200">
        <ButtonBlueMain
          label={isSaving ? "Saving..." : "Save Details"}
          onPress={handleSave}
          bgVariant="primary"
          textVariant="primary"
          className="w-[350px] h-[80px]"
          disabled={isSaving}
        />
      </View>
    </SafeAreaView>
  );
}
