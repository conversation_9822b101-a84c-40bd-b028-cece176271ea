import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  TouchableOpacity,
  SafeAreaView,
  StatusBar,
  TextInput,
  ActivityIndicator,
  Alert,
  Image,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
} from "react-native";
import { Stack, useLocalSearchParams, useRouter } from "expo-router";
import { useUser } from "@clerk/clerk-expo";
import { images } from "@/constants";

const EnterDetailsScreen = () => {
  const router = useRouter();
  const {
    serviceId,
    selectedDate,
    selectedTimeSlot,
    isVIP,
    subUnitId,
    subUnitName,
    subUnitPrice
  } = useLocalSearchParams<{
    serviceId: string;
    selectedDate: string;
    selectedTimeSlot: string;
    isVIP: string;
    subUnitId?: string;
    subUnitName?: string;
    subUnitPrice?: string;
  }>();
  const { user } = useUser();
  const [mobileNumber, setMobileNumber] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [service, setService] = useState<any>(null);

  // Fetch service details
  useEffect(() => {
    const fetchServiceDetails = async () => {
      try {
        const response = await fetch(
          `http://***************:3000/api/partner/services/${serviceId}`
        );
        if (response.ok) {
          const data = await response.json();
          setService(data);
        }
      } catch (error) {
        console.error("Error fetching service details:", error);
      }
    };

    fetchServiceDetails();
  }, [serviceId]);

  const [fullName, setFullName] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);

  useEffect(() => {
    if (user && user.primaryEmailAddress) {
      fetchUserDetails();
    } else {
      setIsLoading(false);
    }
  }, [user]);

  const fetchUserDetails = async () => {
    try {
      setIsLoading(true);

      if (!user || !user.primaryEmailAddress) {
        setIsLoading(false);
        return;
      }

      const email = user.primaryEmailAddress.emailAddress;

      const response = await fetch(
        `http://***************:3000/api/customer/profile/${email}`
      );

      if (!response.ok) {
        throw new Error("Failed to fetch user details");
      }

      const data = await response.json();

      if (data.success && data.user) {
        if (data.user.fullName) {
          setFullName(data.user.fullName);
        }

        if (data.user.mobileNumber) {
          setMobileNumber(data.user.mobileNumber);
        }
      }
    } catch (error) {
      console.error("Error fetching user details:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const validateInputs = () => {
    if (!fullName.trim()) {
      Alert.alert("Error", "Please enter your full name");
      return false;
    }

    // Simple mobile number validation
    if (!mobileNumber.trim() || !/^\+?[0-9]{10,15}$/.test(mobileNumber.trim())) {
      Alert.alert("Error", "Please enter a valid mobile number");
      return false;
    }

    return true;
  };

  const handleSubmit = async () => {
    if (!user?.id) {
      Alert.alert("Error", "Please sign in to join the queue");
      return;
    }

    if (!validateInputs()) {
      return;
    }

    try {
      setIsSubmitting(true);
      setIsLoading(true);

      // Update the user details in the backend first
      if (user && user.primaryEmailAddress) {
        const email = user.primaryEmailAddress.emailAddress;

        console.log("Updating user details:", {
          email,
          fullName,
          mobileNumber
        });

        // Call the update-details API endpoint with proper format
        const updateResponse = await fetch(
          "http://***************:3000/api/customer/update-details",
          {
            method: "PUT",
            headers: {
              "Content-Type": "application/json",
              "Accept": "application/json",
            },
            body: JSON.stringify({
              email: email,
              fullName: fullName,
              mobileNumber: mobileNumber,
            }),
          }
        );

        // Log the raw response for debugging
        console.log("Update response status:", updateResponse.status);

        if (!updateResponse.ok) {
          let errorMessage = "Failed to update user details";
          try {
            const errorData = await updateResponse.json();
            console.error("Error updating user details:", errorData);
            errorMessage = errorData.message || errorMessage;
          } catch (jsonError) {
            console.error("Failed to parse error response:", jsonError);
          }

          console.error("Failed to update user details:", errorMessage);
          // We'll continue with the queue process even if update fails
        } else {
          try {
            const updateResult = await updateResponse.json();
            console.log("User details updated successfully:", updateResult);
          } catch (jsonError) {
            console.error("Failed to parse success response:", jsonError);
          }
        }
      }

      // Fetch service details first
      const serviceResponse = await fetch(
        `http://***************:3000/api/partner/services/${serviceId}`
      );

      if (!serviceResponse.ok) {
        throw new Error("Failed to fetch service details");
      }

      const serviceData = await serviceResponse.json();

      // Fetch time slot details
      // Determine the API endpoint based on whether we're using subunits
      let apiUrl = `http://***************:3000/api/partner/services/${serviceId}/time-slots/${selectedDate}`;

      // If subunit information is available, add the subunit parameter
      if (subUnitId) {
        apiUrl += `?subUnitId=${subUnitId}`;
        console.log(`Using subunit-specific time slots API for subunit ID ${subUnitId} in enter-details`);
      }

      const timeSlotResponse = await fetch(apiUrl);

      if (!timeSlotResponse.ok) {
        const errorText = await timeSlotResponse.text();
        console.error(`Failed to fetch time slot details: ${errorText}`);
        throw new Error(`Failed to fetch time slot details: ${errorText}`);
      }

      const timeSlotData = await timeSlotResponse.json();
      console.log(`Time slot data response:`, JSON.stringify(timeSlotData));

      // Check if timeSlots array exists
      if (!timeSlotData.timeSlots || !Array.isArray(timeSlotData.timeSlots)) {
        console.error(`Invalid time slot data format: ${JSON.stringify(timeSlotData)}`);
        throw new Error(`Invalid time slot data format: No time slots array found`);
      }

      // Find the selected time slot
      const selectedSlot = timeSlotData.timeSlots.find(
        (slot: any) => slot.timeSlot === selectedTimeSlot
      );

      if (!selectedSlot) {
        console.error(`Selected time slot "${selectedTimeSlot}" not found in available slots:`,
          JSON.stringify(timeSlotData.timeSlots.map((s: any) => s.timeSlot)));
        throw new Error(`Selected time slot "${selectedTimeSlot}" not found in available slots`);
      }

      // Prepare params for summary screen
      const params: any = {
        isVIP: isVIP,
        serviceDetails: JSON.stringify(serviceData),
        timeSlotDetails: JSON.stringify(selectedSlot),
        fullName: fullName,
        mobileNumber: mobileNumber,
        selectedDate: selectedDate,
        selectedTimeSlot: selectedTimeSlot,
        serviceId: serviceId
      };

      // Add subunit information if available
      if (subUnitId && subUnitName) {
        params.subUnitId = subUnitId;
        params.subUnitName = subUnitName;
        params.subUnitPrice = subUnitPrice || "0";
        console.log(`Passing subunit to summary: ${subUnitName} (ID: ${subUnitId}, Price: ${subUnitPrice})`);
      }

      // Navigate to summary with all required data, without joining the queue yet
      router.push({
        pathname: "/(root)/summary",
        params
      });
    } catch (error) {
      console.error("Error preparing queue data:", error);
      Alert.alert("Error", "Failed to prepare queue data. Please try again.");
    } finally {
      setIsSubmitting(false);
      setIsLoading(false);
    }
  };

  return (
    <SafeAreaView className="flex-1 bg-white">
      <StatusBar barStyle="dark-content" backgroundColor="white" />

      <Stack.Screen
        options={{
          headerShown: false,
        }}
      />

      <KeyboardAvoidingView
        behavior={Platform.OS === "ios" ? "padding" : "height"}
        className="flex-1"
      >
        <ScrollView className="flex-1">
          <View className="bg-white pb-8">
            <View className="px-8 pt-12">
              <View className="flex-row justify-start mt-4">
                <TouchableOpacity
                  className="flex-row items-center absolute left-0 -top-2 border border-secondary-600 w-12 h-12 rounded-full justify-center"
                  onPress={() => router.back()}
                >
                  <Image source={images.back} className="w-4 h-4" />
                </TouchableOpacity>

                <View className="flex ml-20 items-center">
                  <Text className="font-poppins-medium text-xl mb-2">
                    Enter Details
                  </Text>
                </View>
              </View>
            </View>
          </View>

          {isLoading ? (
            <View className="flex-1 justify-center items-center">
              <ActivityIndicator size="large" color="#159AFF" />
            </View>
          ) : (
            <View className="px-6 pt-4">


              <View className="mb-6">
                <Text className="font-poppins-medium text-secondary-600 mb-2">
                  Full Name
                </Text>
                <TextInput
                  className="border font-poppins-regular border-gray-300 h-[60px] rounded-xl px-6 text-secondary-500"
                  placeholder="Enter your full name"
                  value={fullName}
                  onChangeText={setFullName}
                  autoCapitalize="words"
                  cursorColor="#159AFF"
                />
              </View>

              <View className="mb-6">
                <Text className="font-poppins-medium text-secondary-600 mb-2">
                  Mobile Number
                </Text>
                <View className="flex-row items-center border h-[60px] w-auto border-gray-300 rounded-xl mb-8 ">
                  <View className="bg-secondary-600/20 h-full w-16 items-center justify-center rounded-l-xl">
                    <Text className=" font-poppins-regular text-secondary-500 px-2">
                      +91
                    </Text>
                  </View>
                  <TextInput
                    className=" flex-1 font-poppins-regular text-secondary-500 ml-5 pt-4"
                    placeholder="Enter your mobile number"
                    placeholderTextColor="#62727A"
                    value={mobileNumber}
                    onChangeText={setMobileNumber}
                    keyboardType="phone-pad"
                    maxLength={10}
                    cursorColor="#159AFF"
                  />
                </View>
              </View>

              <View className="mb-4 p-4 bg-gray-100 rounded-xl">
                <Text className="text-secondary-300 text-sm">
                  <Text className="font-poppins-medium">Note:</Text> Your details will be used to notify you about your queue status.
                </Text>
              </View>
            </View>
          )}
        </ScrollView>

        <View className="p-6 border-t flex justify-center items-center border-gray-200">
          <TouchableOpacity
            className={`w-[390px] py-6 rounded-2xl items-center ${!isLoading ? "bg-primary-500" : "bg-gray-300"
              }`}
            onPress={handleSubmit}
            disabled={isLoading || isSubmitting}
          >
            {isSubmitting ? (
              <ActivityIndicator size="small" color="white" />
            ) : (
              <Text className="text-white font-poppins-medium text-lg">
                Next
              </Text>
            )}
          </TouchableOpacity>
        </View>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

export default EnterDetailsScreen;
