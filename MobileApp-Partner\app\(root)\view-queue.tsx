import React, { useState, useEffect, useCallback, useRef } from "react";
import {
  View,
  Text,
  TouchableOpacity,
  SafeAreaView,
  StatusBar,
  ScrollView,
  ActivityIndicator,
  Alert,
  Image,
  TextInput,
  FlatList,
  Linking,

  Modal,
  Animated,
  Easing,
  RefreshControl,
} from "react-native";
import { Stack, useLocalSearchParams, useRouter } from "expo-router";
import { images } from "@/constants";
import { useAuth } from "@clerk/clerk-expo";
import { Ionicons } from "@expo/vector-icons";

// Interface for Queue Member
interface QueueMember {
  id: number;
  fullName: string;
  uniqueSlotId: string;
  status: string;
  isVIP: boolean;
  createdAt: string;
  isCheckedIn?: boolean;
  position?: number;
  mobileNumber?: string;
  queueNumber?: string;
  processedAt?: string; // Timestamp for when a member is processed (for key uniqueness)
  statusUpdatedAt?: string; // Timestamp for when the status was last updated
  serveTime?: number; // Time in minutes to serve this customer
}

// Interface for Queue Overview
interface QueueOverview {
  totalServed: number;
  pendingServing: number;
  noShows: number;
  availableSlots: number;
  currentlyServing: QueueMember | null;
}

// Add these interfaces for grace period handling
interface GracePeriodStatus {
  queueId: number;
  inGracePeriod: boolean;
  confirmedPresence: boolean;
  graceStartedAt: string | null;
  graceEndTime?: string;
  graceTimeSeconds: number;
  remainingSeconds: number;
  isExpired: boolean;
  status: string;
}

// Add interface for serving status
interface ServingStatus {
  queueId: number;
  serviceId: number;
  isServing: boolean;
  servingStartedAt: string | null;
  estimatedEndTime: string | null;
  servingTimeMinutes: number;
  remainingMinutes: number;
  remainingSeconds: number;
  serveTime?: number; // Time in minutes to serve this specific customer
}

export default function ViewQueueScreen() {
  const router = useRouter();
  const { timeSlot, date, status, serviceId } = useLocalSearchParams();
  const { isSignedIn } = useAuth();

  // Create an isMounted ref to avoid memory leaks
  const isMounted = useRef(true);

  // State variables
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [overview, setOverview] = useState<QueueOverview>({
    totalServed: 0,
    pendingServing: 0,
    noShows: 0,
    availableSlots: 0,
    currentlyServing: null,
  });
  const [waitingMembers, setWaitingMembers] = useState<QueueMember[]>([]);
  const [servedMembers, setServedMembers] = useState<QueueMember[]>([]);
  const [searchQuery, setSearchQuery] = useState("");
  const [activeTab, setActiveTab] = useState<"waiting" | "served">("waiting");
  const [serviceDetails, setServiceDetails] = useState<any>(null);

  // Verification modal state
  const [verificationModalVisible, setVerificationModalVisible] =
    useState(false);
  const [verificationSlotId, setVerificationSlotId] = useState("");
  const [selectedQueueMember, setSelectedQueueMember] =
    useState<QueueMember | null>(null);
  const [verificationError, setVerificationError] = useState("");
  // Add ref for input fields
  const inputRefs = useRef<Array<TextInput | null>>([]);
  // Add state for validation
  const [invalidInput, setInvalidInput] = useState(false);

  // No Show options modal state
  const [noShowModalVisible, setNoShowModalVisible] = useState(false);
  const [noShowQueueMember, setNoShowQueueMember] = useState<QueueMember | null>(null);

  // Animation values
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(0.9)).current;

  // New loading states
  const [isNoShowLoading, setIsNoShowLoading] = useState(false);
  const [isMarkServedLoading, setIsMarkServedLoading] = useState(false);
  const [isVerifyLoading, setIsVerifyLoading] = useState(false);

  // Add to the component state (where the other state definitions are)
  const [inGracePeriod, setInGracePeriod] = useState(false);
  const [gracePeriodStatus, setGracePeriodStatus] =
    useState<GracePeriodStatus | null>(null);
  const [gracePeriodTimer, setGracePeriodTimer] =
    useState<NodeJS.Timeout | null>(null);
  const [remainingGraceTime, setRemainingGraceTime] = useState(120); // Default 2 minutes

  const [servingStatus, setServingStatus] = useState<ServingStatus | null>(
    null
  );
  const [remainingServingTime, setRemainingServingTime] = useState(0);
  const [initialServingTime, setInitialServingTime] = useState(0);
  const [servingStartedAt, setServingStartedAt] = useState<string | null>(null);
  const [servingTimer, setServingTimer] = useState<NodeJS.Timeout | null>(null);

  // Add a flag to track if we're verifying for service start or completion
  const [isStartingService, setIsStartingService] = useState(false);

  // Helper function to parse time to minutes
  const parseTimeToMinutes = (timeString: string) => {
    try {
      // Format: "9:00 AM" or "12:00 PM"
      const [timeValue, period] = timeString.split(" ");
      const [hourStr, minuteStr] = timeValue.split(":");
      let hour = parseInt(hourStr, 10);
      const minute = parseInt(minuteStr, 10);

      if (period.toUpperCase() === "PM" && hour < 12) {
        hour += 12;
      } else if (period.toUpperCase() === "AM" && hour === 12) {
        hour = 0;
      }

      return hour * 60 + minute;
    } catch (err) {
      console.error("Error parsing time:", err);
      return 0;
    }
  };

  let fetchQueueData: (shouldResetState?: boolean) => Promise<void>;

  // Modified fetch queue data to support preserving UI state
  fetchQueueData = useCallback(async (shouldResetState = true) => {
    if (!serviceId || !date || !timeSlot) {
      Alert.alert("Error", "Missing required parameters");
      return;
    }

    if (!isSignedIn || !isMounted.current) return;

    // Only set loading state if this is the initial load (when the screen is truly empty)
    const isInitialLoad =
      isLoading && waitingMembers.length === 0 && servedMembers.length === 0;

    if (isInitialLoad) {
      setIsLoading(true);
    } else if (!isRefreshing) {
      // For regular refreshes, just use the refresh indicator
      setIsRefreshing(true);
    }

    try {
      // Only reset state if explicitly requested (initial load or forced reset)
      if (shouldResetState) {
        // Before fetching new data, clear timers to prevent state conflicts
        if (gracePeriodTimer) {
          clearInterval(gracePeriodTimer);
          setGracePeriodTimer(null);
        }

        if (servingTimer) {
          clearInterval(servingTimer);
          setServingTimer(null);
        }

        // Reset serving and grace period states to default
        setServingStatus(null);
        setInGracePeriod(false);
        setGracePeriodStatus(null);
      }

      // Before fetching new data, clear timers to prevent state conflicts
      if (gracePeriodTimer) {
        clearInterval(gracePeriodTimer);
        setGracePeriodTimer(null);
      }

      if (servingTimer) {
        clearInterval(servingTimer);
        setServingTimer(null);
      }

      // Fetch service details first to get serving time
      const serviceResponse = await fetch(
        `${API_BASE_URL}/partner/services/${serviceId}`
      );

      if (!serviceResponse.ok) {
        console.error(`Service API returned status: ${serviceResponse.status}`);
        throw new Error(
          `Failed to fetch service details: ${serviceResponse.status}`
        );
      }

      const serviceData = await serviceResponse.json();

      // Only update if component is still mounted
      if (!isMounted.current) return;
      setServiceDetails(serviceData);

      // Add timestamp to prevent caching
      const timestamp = new Date().getTime();

      // First, try to ensure data consistency to fix any corrupted queue statuses
      try {
        console.log(`Ensuring data consistency for service ${serviceId}`);
        await fetch(
          `${API_BASE_URL}/customer/queues/ensure-consistency/${serviceId}`
        );
      } catch (error) {
        console.log(
          "Failed to ensure consistency, continuing with regular fetch:",
          error
        );
      }

      // Check if still mounted before continuing
      if (!isMounted.current) return;

      console.log(
        `Fetching queues from: ${API_BASE_URL}/customer/queues/redis/${serviceId}/all?t=${timestamp}`
      );

      // Fetch all queues for this service from Redis with timestamp to avoid caching
      const queuesResponse = await fetch(
        `${API_BASE_URL}/customer/queues/redis/${serviceId}/all?t=${timestamp}`
      );

      if (!queuesResponse.ok) {
        console.error(`Queue API returned status: ${queuesResponse.status}`);
        const errorText = await queuesResponse.text();
        console.error(`Error response: ${errorText}`);
        throw new Error(`Failed to fetch queues: ${queuesResponse.status}`);
      }

      const queuesData = await queuesResponse.json();
      console.log("API response:", JSON.stringify(queuesData));

      // Check if still mounted before continuing with state updates
      if (!isMounted.current) return;

      if (queuesData.status !== "success") {
        console.error("API returned non-success status:", queuesData.status);
        throw new Error(
          `API returned error: ${queuesData.message || "Unknown error"}`
        );
      }

      if (!queuesData.queues || !Array.isArray(queuesData.queues)) {
        console.error("Invalid queue data format:", queuesData);
        throw new Error("Invalid queue data format returned from API");
      }

      // Log the queue data length
      console.log(`Received ${queuesData.queues.length} queues from API`);

      // Log the first queue for debugging
      if (queuesData.queues.length > 0) {
        const sampleQueue = queuesData.queues[0];
        console.log("Sample queue data:", JSON.stringify(sampleQueue));
        console.log("Queue fields:", Object.keys(sampleQueue));
        console.log("Check-in status:", {
          isCheckedIn: sampleQueue.isCheckedIn,
          status: sampleQueue.status,
        });

        // Count queues by status for debugging
        const statusCounts = queuesData.queues.reduce((counts: any, q: any) => {
          const status = q.status || "unknown";
          counts[status] = (counts[status] || 0) + 1;
          return counts;
        }, {});
        console.log("Queue status counts:", statusCounts);
      }

      // Check if we have no queues - this could mean the Redis data is missing
      // Trigger a recovery if the queues array is empty or very small
      if (
        queuesData.queues.length === 0 ||
        (isInitialLoad &&
          queuesData.queues.length < 3 &&
          queuesData.queues.every((q: any) => !q.fullName))
      ) {
        console.log(
          "Possible data corruption - trying to recover data from database"
        );

        try {
          // Force a consistency check and data restoration
          const recoveryResponse = await fetch(
            `${API_BASE_URL}/customer/queues/ensure-consistency/${serviceId}?force=true`
          );

          if (recoveryResponse.ok) {
            console.log("Recovery completed, refetching data");
            // Wait a moment for the recovery to complete
            await new Promise((resolve) => setTimeout(resolve, 1000));

            // Refetch the queue data
            const refreshResponse = await fetch(
              `${API_BASE_URL}/customer/queues/redis/${serviceId}/all?t=${Date.now()}`
            );

            if (refreshResponse.ok) {
              const refreshData = await refreshResponse.json();
              if (
                refreshData.status === "success" &&
                refreshData.queues &&
                Array.isArray(refreshData.queues) &&
                refreshData.queues.length > 0
              ) {
                console.log(
                  `Recovery successful, got ${refreshData.queues.length} queues`
                );
                queuesData.queues = refreshData.queues;
              }
            }
          }
        } catch (recoveryError) {
          console.error("Failed during data recovery attempt:", recoveryError);
        }
      }

      // Filter queues for the selected date and time slot
      const filteredQueues = queuesData.queues.filter((queue: any) => {
        if (!queue || !queue.date || !queue.timeSlot) return false;

        // Parse date to YYYY-MM-DD format
        let queueDate;
        try {
          if (typeof queue.date === "string") {
            queueDate = queue.date.split("T")[0];
    } else {
            queueDate = new Date(queue.date).toISOString().split("T")[0];
          }
        } catch (err) {
          console.error("Error parsing queue date:", err);
          return false;
        }

        return queueDate === date && queue.timeSlot === String(timeSlot);
      });

      console.log(
        `Filtered ${filteredQueues.length} queues for date: ${date}, time slot: ${timeSlot}`
      );

      // Get all waiting/checked-in/serving queues
      const waitingOrServingQueues = filteredQueues.filter(
        (q: any) =>
          q.status === "waiting" ||
          q.status === "checked-in" ||
          q.status === "serving"
      );

      // Sort them by position
      const sortedQueues = [...waitingOrServingQueues].sort((a, b) => {
        // First sort by position
        const positionDiff = (a.position || 0) - (b.position || 0);

        // If positions are the same, then consider VIP status
        if (positionDiff === 0) {
          if (a.isVIP && !b.isVIP) return -1;
          if (!a.isVIP && b.isVIP) return 1;
        }

        return positionDiff;
      });

      // Get the first one as currently serving
      const currentlyServing = sortedQueues.length > 0 ? sortedQueues[0] : null;

      // Log the currently serving member
      if (currentlyServing) {
        console.log(`Currently serving: id=${currentlyServing.id}, position=${currentlyServing.position}, isVIP=${currentlyServing.isVIP}`);
      } else {
        console.log("No currently serving member");
      }

      // Count stats - explicitly count by status, include all relevant statuses
      const totalServed = filteredQueues.filter(
        (q: any) => q.status === "completed"
      ).length;

      const pendingServing = filteredQueues.filter(
        (q: any) =>
          q.status === "waiting" ||
          q.status === "checked-in" ||
          q.status === "serving"
      ).length;

      const noShows = filteredQueues.filter(
        (q: any) => q.status === "no-show"
      ).length;

      console.log(
        `Queue stats - Completed: ${totalServed}, Pending: ${pendingServing}, No Shows: ${noShows}`
      );

      // Calculate available slots based on time slot duration and service serving time
      let availableSlots = 0;

      // Extract start and end times
      const timeSlotParts = String(timeSlot).split(" - ");
      if (timeSlotParts.length === 2) {
        const startTimePart = timeSlotParts[0].trim();
        const endTimePart = timeSlotParts[1].trim();

        const startTime = parseTimeToMinutes(startTimePart);
        const endTime = parseTimeToMinutes(endTimePart);

        // Calculate duration in minutes
        const duration = endTime - startTime;

        // Get serving time from service details (default to 15 min if not available)
        const servingTime = serviceData.queueInfo?.servingTime || 15;

        // Calculate max slots (subtract 1 for safety)
        availableSlots = Math.max(0, Math.floor(duration / servingTime) - 1);
      }

      // Set overview with properly mapped currently serving member using our mapping function
      const currentlyServingMember = currentlyServing
        ? await mapQueueToMember(currentlyServing)
        : null;

      if (isMounted.current) {
        setOverview({
          totalServed,
          pendingServing,
          noShows,
          availableSlots,
          currentlyServing: currentlyServingMember,
        });
      }

      // Process waiting members - include the 'serving' status too
      const waitingQueues = filteredQueues.filter(
        (q: any) =>
          q.status === "waiting" ||
          q.status === "checked-in" ||
          q.status === "serving"
      );
      console.log(`Processing ${waitingQueues.length} waiting queues`);

      // Process served members - make sure to include both 'completed' AND 'no-show' status
      const servedQueues = filteredQueues.filter(
        (q: any) => q.status === "completed" || q.status === "no-show"
      );
      console.log(
        `Processing ${servedQueues.length} served queues (completed or no-show)`
      );

      // Log served queue statuses for debugging
      servedQueues.forEach((q: any, index: number) => {
        console.log(
          `Served queue ${index}: id=${q.id}, status=${q.status}, name=${q.fullName || "Anonymous"}`
        );
      });

      // Map all queue members in parallel
      let waitingMembers = await Promise.all(
        waitingQueues.map(mapQueueToMember)
      );
      const servedMembers = await Promise.all(
        servedQueues.map(mapQueueToMember)
      );

      // Sort waiting members by position
      waitingMembers = waitingMembers.sort((a, b) => {
        // First sort by position
        const positionDiff = (a.position || 0) - (b.position || 0);

        // If positions are the same, then consider VIP status
        if (positionDiff === 0) {
          if (a.isVIP && !b.isVIP) return -1;
          if (!a.isVIP && b.isVIP) return 1;
        }

        return positionDiff;
      });

      // Log positions for debugging
      console.log("Sorted waiting members by position:");
      waitingMembers.forEach((member, index) => {
        console.log(`Member ${index}: id=${member.id}, position=${member.position}, isVIP=${member.isVIP}`);
      });

      console.log(
        `Mapped ${waitingMembers.length} waiting members and ${servedMembers.length} served members`
      );

      // Double-check member data
      if (
        servedMembers.length > 0 &&
        servedMembers.every((m) => m.fullName === "Anonymous")
      ) {
        console.warn(
          "All served members have Anonymous names, possible data corruption"
        );
      }

      // Final state updates only if component is still mounted
      if (!isMounted.current) return;

      // No need to sort again, we already sorted above
      const sortedWaitingMembers = [...waitingMembers];

      setWaitingMembers(sortedWaitingMembers);
      setServedMembers(servedMembers);
    } catch (error) {
      if (!isMounted.current) return;

      console.error("Error fetching queue data:", error);
      Alert.alert(
        "Error",
        "Failed to load queue information. Please try again."
      );
    } finally {
      // Only update loading states if still mounted
      if (!isMounted.current) return;

      if (isInitialLoad) {
        setIsLoading(false);
      }
      setIsRefreshing(false);
    }
  }, [
    serviceId,
    date,
    timeSlot,
    isSignedIn,
    isLoading,
    waitingMembers.length,
    servedMembers.length,
  ]);

  // Add a debounce mechanism for refreshes
  const isRefreshingRef = useRef(false);
  const refreshTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Debounced refresh function
  const debouncedRefresh = useCallback((immediate = false) => {
    // Clear any pending refresh
    if (refreshTimeoutRef.current) {
      clearTimeout(refreshTimeoutRef.current);
      refreshTimeoutRef.current = null;
    }

    // If already refreshing, don't queue another refresh
    if (isRefreshingRef.current && !immediate) {
      console.log("Refresh already in progress, skipping");
      return;
    }

    const doRefresh = async () => {
      if (!isMounted.current) return;

      console.log("Executing debounced refresh");
      isRefreshingRef.current = true;

      try {
        await fetchQueueData(false);
      } finally {
        if (isMounted.current) {
          isRefreshingRef.current = false;
        }
      }
    };

    if (immediate) {
      doRefresh();
    } else {
      // Set a timeout to actually do the refresh
      refreshTimeoutRef.current = setTimeout(doRefresh, 500);
    }
  }, [fetchQueueData]);

  // Cleanup refresh timers on unmount
  useEffect(() => {
    return () => {
      if (refreshTimeoutRef.current) {
        clearTimeout(refreshTimeoutRef.current);
      }
    };
  }, []);

  // Improved refresh function to prevent multiple refreshes
  const onRefresh = useCallback(async () => {
    if (!isMounted.current) return;
    console.log("Manual refresh triggered");

    // Set refreshing flag but don't reset any other UI state
    setIsRefreshing(true);

    try {
      // Force an immediate refresh
      await debouncedRefresh(true);
    } finally {
      if (isMounted.current) {
        setIsRefreshing(false);
      }
    }
  }, [debouncedRefresh]);

  // Replace individual refresh calls with debounced version
  const performFastDataRefresh = useCallback(() => {
    console.log("Performing fast data refresh");

    // Instead of immediate refresh, use the debounced version
    debouncedRefresh();

    // Immediate UI update logic for better UX can still remain
    if (overview?.currentlyServing) {
      const currentMember = overview.currentlyServing;

      // Add a timestamp for uniqueness if not already present
      const updatedMember = {
        ...currentMember,
        status: "completed", // Explicitly set status to completed
        processedAt: new Date().toISOString(), // Add a unique timestamp for key generation
      };

      // Remove from waiting members
      setWaitingMembers((prev) =>
        prev.filter((m) => m.id !== currentMember.id)
      );

      // Update overview - clear the currently serving member
      setOverview((prev) => {
        if (!prev) return prev;
        return {
          ...prev,
          totalServed: prev.totalServed + 1,
          pendingServing: Math.max(0, prev.pendingServing - 1),
          currentlyServing: null, // Clear currently serving
        };
      });

      // Add to served members
      setServedMembers((prev) => {
        // Ensure fullName is properly set before adding to served members
        const memberWithFullName = {
          ...updatedMember,
          // Use type assertion to safely access potential userName property
          fullName:
            updatedMember.fullName ||
            (updatedMember as any).userName ||
            "Anonymous",
        };

        console.log(
          "Adding completed member to served list:",
          memberWithFullName
        );

        // Put the new item at the beginning of the list with updated timestamp
        return [memberWithFullName, ...prev];
      });
    }
  }, [overview?.currentlyServing, debouncedRefresh]);

  // Load data on initial render and set up auto-refresh
  useEffect(() => {
    // Set isMounted to true - important for tracking component lifecycle
    isMounted.current = true;

    if (isSignedIn) {
      // Initial data load
      debouncedRefresh(true); // Force immediate first load

      // Set up auto-refresh using the debounced refresh mechanism
      const refreshInterval = setInterval(() => {
        if (isMounted.current) {
          console.log("Auto-refreshing queue data...");
          // Use the same debounced refresh for consistent behavior
          debouncedRefresh(false);
        }
      }, 15000); // 15 seconds

      // Clean up the interval when component unmounts
      return () => {
        clearInterval(refreshInterval);
        // Mark component as unmounted
        isMounted.current = false;
      };
    }

    // Clean up when unmounting if no refresh interval was set
    return () => {
      isMounted.current = false;
    };
  }, [debouncedRefresh, isSignedIn]);

  // Component cleanup on unmount - Single unified cleanup effect
  useEffect(() => {
    // Return cleanup function
    return () => {
      console.log("Component unmounting - cleaning up all resources");

      // Clear all intervals and timers
      if (gracePeriodTimer) {
        clearInterval(gracePeriodTimer);
      }
      if (servingTimer) {
        clearInterval(servingTimer);
      }

      // Set flag to prevent state updates after unmount
      isMounted.current = false;
    };
  }, []); // Empty dependency array ensures this only runs on mount/unmount

  // Function to close modal with animation
  const closeModalWithAnimation = () => {
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 0,
        duration: 250,
        easing: Easing.bezier(0.25, 0.1, 0.25, 1),
        useNativeDriver: true,
      }),
      Animated.timing(scaleAnim, {
        toValue: 0.9,
        duration: 250,
        easing: Easing.bezier(0.25, 0.1, 0.25, 1),
        useNativeDriver: true,
      }),
    ]).start(() => {
      setVerificationModalVisible(false);

      // Properly reset state when modal is closed
      setVerificationSlotId("");
      setVerificationError("");
      setInvalidInput(false);
      setIsStartingService(false);
      setIsVerifyLoading(false);
    });
  };

  // API base URL
  const API_BASE_URL = "http://192.168.250.149:3000/api";

  // Helper function to directly get the latest check-in status for a specific queue
  const fetchLatestCheckInStatus = async (
    queueId: number
  ): Promise<boolean | null> => {
    try {
      // Add timestamp to prevent caching
      const timestamp = new Date().getTime();
      const response = await fetch(
        `${API_BASE_URL}/customer/queue/${queueId}?t=${timestamp}`
      );

      if (!response.ok) {
        console.error(
          `Failed to fetch latest check-in status for queue ${queueId}`
        );
        return null;
      }

      const data = await response.json();

      if (data.status === "success" && data.queue) {
        const isCheckedIn =
          data.queue.isCheckedIn === true || data.queue.status === "checked-in";
        console.log(
          `Latest check-in status for queue ${queueId}: ${isCheckedIn ? "Checked In" : "Not Checked In"}`
        );
        return isCheckedIn;
      }

      return null;
    } catch (error) {
      console.error(
        `Error fetching latest check-in status for queue ${queueId}:`,
        error
      );
      return null;
    }
  };

  // Enhanced function to fetch queue details from database, with better error handling
  const fetchQueueDetails = async (queueId: number): Promise<any> => {
    try {
      // First, try to get the queue details directly
      const response = await fetch(`${API_BASE_URL}/customer/queue/${queueId}`);

      if (!response.ok) {
        throw new Error(`Failed to fetch queue details for ID ${queueId}`);
      }

      const data = await response.json();

      if (data.status === "success" && data.queue) {
        console.log(
          `Successfully fetched details for queue ${queueId} from database`
        );

        // If we have a uniqueSlotId but no userName, try fetching by uniqueSlotId
        if (
          (!data.queue.userName || !data.queue.fullName) &&
          data.queue.uniqueSlotId
        ) {
          try {
            console.log(
              `Attempting to fetch queue by uniqueSlotId: ${data.queue.uniqueSlotId}`
            );
            const uniqueSlotResponse = await fetch(
              `${API_BASE_URL}/customer/queue-by-uniqueslotid/${data.queue.uniqueSlotId}`
            );

            if (uniqueSlotResponse.ok) {
              const uniqueSlotData = await uniqueSlotResponse.json();
              if (
                uniqueSlotData.status === "success" &&
                uniqueSlotData.queue &&
                uniqueSlotData.queue.userName
              ) {
                console.log(
                  `Found user name "${uniqueSlotData.queue.userName}" by uniqueSlotId`
                );
                // Update our queue data with the user name
                data.queue.userName = uniqueSlotData.queue.userName;
                data.queue.fullName = uniqueSlotData.queue.userName; // Use userName as fallback for fullName too
              }
            }
          } catch (error) {
            console.error(
              `Error fetching queue by uniqueSlotId ${data.queue.uniqueSlotId}:`,
              error
            );
          }
        }

        // If no mobile number, directly use our specialized endpoint
        if (!data.queue.mobileNumber) {
          try {
            console.log(`Directly fetching mobile number for queue ${queueId}`);
            const mobileResponse = await fetch(
              `${API_BASE_URL}/customer/queue-mobile/${queueId}`
            );

            if (mobileResponse.ok) {
              const mobileData = await mobileResponse.json();
              if (
                mobileData.status === "success" &&
                mobileData.data &&
                mobileData.data.mobileNumber
              ) {
                console.log(
                  `Found mobile number from direct endpoint: ${mobileData.data.mobileNumber}`
                );
                // Update our queue data with the mobile number
                data.queue.mobileNumber = mobileData.data.mobileNumber;

                // Also update the name if we have a better one
                if (mobileData.data.fullName && !data.queue.fullName) {
                  data.queue.fullName = mobileData.data.fullName;
                }
              } else {
                console.log(`No mobile number found for queue ${queueId}`);
              }
            }
          } catch (error) {
            console.error(
              `Error fetching mobile number for queue ${queueId}:`,
              error
            );
          }
        }

        return data.queue;
      }

      throw new Error("Invalid response format");
    } catch (error) {
      console.error(`Error fetching queue details for ${queueId}:`, error);
      return null;
    }
  };

  // Helper function to check if a string looks like an email
  const isEmail = (text: string) => {
    return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(text);
  };

  // New helper function to get user data by ID or email
  const fetchUserByIdentifier = async (identifier: string): Promise<any> => {
    if (!identifier) return null;

    try {
      // If identifier looks like an email, use profile endpoint
      if (isEmail(identifier)) {
        const response = await fetch(
          `${API_BASE_URL}/customer/profile/${encodeURIComponent(identifier)}`
        );

        if (!response.ok) {
          console.log(`No user found with email ${identifier}`);
          return null;
        }

        const data = await response.json();
        if (data.status === "success" && data.user) {
          console.log(`Found user profile for email ${identifier}`);
          return data.user;
        }
      }
      // Otherwise, try the user queues endpoint to get details from the first queue
      else {
        const response = await fetch(
          `${API_BASE_URL}/customer/queues/${identifier}`
        );

        if (!response.ok) {
          console.log(`No queues found for user ID ${identifier}`);
          return null;
        }

        const data = await response.json();
        if (
          data.status === "success" &&
          data.queues &&
          data.queues.upcoming &&
          data.queues.upcoming.length > 0
        ) {
          // Extract user name from the first queue
          const firstName =
            data.queues.upcoming[0].userName ||
            data.queues.upcoming[0].fullName;

          if (firstName) {
            console.log(
              `Found user name "${firstName}" from queues for user ID ${identifier}`
            );
            return { fullName: firstName };
          }
        }
      }
      return null;
    } catch (error) {
      console.error(
        `Error fetching user data for identifier ${identifier}:`,
        error
      );
      return null;
    }
  };

  // Map queue data to QueueMember format
  const mapQueueToMember = async (queue: any): Promise<QueueMember> => {
    // Start with basic queue info
    let fullName = queue.fullName || queue.userName || "";
    let mobileNumber = queue.mobileNumber || null;

    console.log(
      `Mapping queue ${queue.id} with status '${queue.status}', fullName: '${fullName}', userName: '${queue.userName || "not set"}', queue.fullName: '${queue.fullName || "not set"}'`
    );

    // Carefully preserve the isCheckedIn value - check both properties
    // queue.isCheckedIn directly from Redis and queue.status for 'checked-in' status
    let isCheckedIn = false;
    if (queue.isCheckedIn === true) {
      isCheckedIn = true;
    } else if (queue.status === "checked-in") {
      isCheckedIn = true;
    }

    // Try to get the latest check-in status directly
    const latestCheckInStatus = await fetchLatestCheckInStatus(queue.id);
    if (latestCheckInStatus !== null) {
      isCheckedIn = latestCheckInStatus;
    }

    console.log(
      `Mapping queue ${queue.id} with status '${queue.status}', isCheckedIn: ${isCheckedIn}`
    );

    // Try to fetch the unique slot ID if not already present
    let uniqueSlotId = queue.uniqueSlotId || "";
    if (!uniqueSlotId && queue.id) {
      try {
        const uniqueSlotResponse = await fetch(
          `${API_BASE_URL}/customer/queue-by-uniqueslotid/${queue.id}`
        );

        if (uniqueSlotResponse.ok) {
          const uniqueSlotData = await uniqueSlotResponse.json();
          if (
            uniqueSlotData.status === "success" &&
            uniqueSlotData.queue &&
            uniqueSlotData.queue.uniqueSlotId
          ) {
            uniqueSlotId = uniqueSlotData.queue.uniqueSlotId;
          }
        }
      } catch (error) {
        console.error("Error fetching unique slot ID:", error);
      }
    }

    // If we have a user ID but no mobile number, try to fetch it
    if (queue.userId && !mobileNumber) {
      try {
        const mobileResponse = await fetch(
          `${API_BASE_URL}/customer/user-mobile/${queue.userId}`
        );

        if (mobileResponse.ok) {
          const mobileData = await mobileResponse.json();
          if (
            mobileData.status === "success" &&
            mobileData.data &&
            mobileData.data.mobileNumber
          ) {
            mobileNumber = mobileData.data.mobileNumber;

            // If we also found a full name and don't have one, use it
            if (!fullName && mobileData.data.fullName) {
              fullName = mobileData.data.fullName;
            }
          }
        }
      } catch (error) {
        console.error("Error fetching mobile number:", error);
      }
    }

    // If we still don't have a mobile number, try a different approach
    // using the queue-mobile endpoint
    if (!mobileNumber && queue.id) {
      try {
        console.log(`Attempting to fetch mobile number for queue ${queue.id}`);
        const mobileResponse = await fetch(
          `${API_BASE_URL}/customer/queue-mobile/${queue.id}`
        );

        if (mobileResponse.ok) {
          const mobileData = await mobileResponse.json();
          if (
            mobileData.status === "success" &&
            mobileData.data &&
            mobileData.data.mobileNumber
          ) {
            mobileNumber = mobileData.data.mobileNumber;

            // If we also found a full name and don't have one, use it
            if (!fullName && mobileData.data.fullName) {
              fullName = mobileData.data.fullName;
            }
          }
        }
      } catch (error) {
        console.error("Error fetching mobile number via queue-mobile:", error);
      }
    }

    // Format queue number (could be position, 4-digit code, etc.)
    const queueNumber = uniqueSlotId || String(queue.id || "");

    // Create the queue member object
    const member: QueueMember = {
      id: queue.id,
      fullName,
      uniqueSlotId,
      status: queue.status || "unknown", // Ensure status is preserved and has a default
      isVIP: !!queue.isVIP,
      createdAt: queue.createdAt || new Date().toISOString(),
      isCheckedIn,
      position: queue.position || 0,
      mobileNumber,
      queueNumber,
      statusUpdatedAt:
        queue.statusUpdatedAt ||
        queue.statusLastUpdatedAt ||
        queue.updatedAt ||
        queue.createdAt ||
        new Date().toISOString(),
    };

    console.log(
      `Mapped queue ${queue.id} to member with status: ${member.status}`
    );
    return member;
  };

  // Mark a queue as completed - direct completion without verification
  const markAsCompleted = async (queueId: number) => {
    setIsMarkServedLoading(true);
    try {
      // Call the complete API endpoint directly
      const response = await fetch(
        `${API_BASE_URL}/partner/queues/${queueId}/complete`,
        {
          method: "PUT",
          headers: {
            "Content-Type": "application/json",
          },
        }
      );

      if (!response.ok) {
        throw new Error("Failed to mark as completed");
      }

      // Show success message
      Alert.alert("Success", "Service completed successfully");

      // Use debounced refresh
      debouncedRefresh();
    } catch (error) {
      console.error("Error completing service:", error);
      Alert.alert("Error", "Failed to complete service");
    } finally {
      setIsMarkServedLoading(false);
    }
  };

  // Verify and complete queue
  const verifyAndCompleteQueue = async () => {
    if (!selectedQueueMember) {
      closeModalWithAnimation();
      return;
    }

    try {
      setIsVerifyLoading(true);
      setVerificationError("");
      setInvalidInput(false);

      // Only verify slot ID if we're starting service
      if (isStartingService) {
        // Check if the verification ID matches
        const slotIdToCheck = verificationSlotId.trim();
        const memberSlotId = selectedQueueMember.uniqueSlotId;

        // Extract just the number part if it's in the format "SLOT-1234"
        const memberSlotNumber =
          memberSlotId && memberSlotId.includes("-")
            ? memberSlotId.split("-").pop()
            : memberSlotId;

        console.log(
          `Verifying: Entered=${slotIdToCheck}, Member=${memberSlotId}, Extracted=${memberSlotNumber}`
        );

        // Check if the entered code matches exactly or is the last 4 digits
        const lastFourDigits = memberSlotNumber ? memberSlotNumber.slice(-4) : "";

        if (
          slotIdToCheck !== memberSlotId &&
          slotIdToCheck !== memberSlotNumber &&
          slotIdToCheck !== lastFourDigits
        ) {
          setVerificationError("Invalid Slot ID. Please check and try again.");
          setIsVerifyLoading(false);
          setInvalidInput(true);

          // Clear with a slight delay and focus first input
          setTimeout(() => {
            setVerificationSlotId("");
            inputRefs.current[0]?.focus?.();
          }, 10);
          return;
        }

        // If verification successful, start serving
        const response = await fetch(
          `${API_BASE_URL}/partner/queues/${selectedQueueMember.id}/start-serving`,
          {
            method: "PUT",
            headers: {
              "Content-Type": "application/json",
            },
          }
        );

        if (!response.ok) {
          throw new Error("Failed to start serving");
        }

        // Show success message
        setTimeout(() => {
          Alert.alert("Success", "Started serving customer");
        }, 300);
      } else {
        // If we're just completing service, call complete endpoint directly
        const response = await fetch(
          `${API_BASE_URL}/partner/queues/${selectedQueueMember.id}/complete`,
          {
            method: "PUT",
            headers: {
              "Content-Type": "application/json",
            },
          }
        );

        if (!response.ok) {
          throw new Error("Failed to complete service");
        }

        // Show success message
        setTimeout(() => {
          Alert.alert("Success", "Service completed successfully");
        }, 300);
      }

      // Close modal with animation
      closeModalWithAnimation();

      // Refresh data
      fetchQueueData();
    } catch (error) {
      console.error("Error completing action:", error);
      setVerificationError(
        isStartingService
          ? "Failed to start serving. Please try again."
          : "Failed to complete service. Please try again."
      );
      setIsVerifyLoading(false);
      // Reset loading state on error
      if (isStartingService) {
        setIsStartingService(false);
      }
    } finally {
      setIsVerifyLoading(false);
    }
  };

  // Open the No Show options modal
  const openNoShowModal = (member: QueueMember) => {
    setNoShowQueueMember(member);
    setNoShowModalVisible(true);

    // Animate the modal opening
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 300,
        easing: Easing.bezier(0.25, 0.1, 0.25, 1),
        useNativeDriver: true,
      }),
      Animated.timing(scaleAnim, {
        toValue: 1,
        duration: 300,
        easing: Easing.bezier(0.25, 0.1, 0.25, 1),
        useNativeDriver: true,
      }),
    ]).start();
  };

  // Close the No Show options modal with animation
  const closeNoShowModal = () => {
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 0,
        duration: 250,
        easing: Easing.bezier(0.25, 0.1, 0.25, 1),
        useNativeDriver: true,
      }),
      Animated.timing(scaleAnim, {
        toValue: 0.9,
        duration: 250,
        easing: Easing.bezier(0.25, 0.1, 0.25, 1),
        useNativeDriver: true,
      }),
    ]).start(() => {
      setNoShowModalVisible(false);
      setNoShowQueueMember(null);
    });
  };

  // Move queue member to the end of the line
  const moveToEndOfLine = async (queueId: number) => {
    setIsNoShowLoading(true);
    try {
      // First fetch the latest queue details to ensure we have the most up-to-date data
      console.log(
        `Fetching latest queue details for queue ${queueId} before moving to end of line`
      );
      const detailsResponse = await fetch(
        `${API_BASE_URL}/customer/queue/${queueId}`
      );

      if (!detailsResponse.ok) {
        throw new Error("Failed to fetch latest queue details");
      }

      const detailsData = await detailsResponse.json();
      if (detailsData.status !== "success" || !detailsData.queue) {
        throw new Error("Failed to get queue details");
      }

      const latestQueue = detailsData.queue;
      console.log(
        `Latest queue data fetched for ${queueId}, status: ${latestQueue.status}`
      );

      // Now proceed with moving to end of line
      console.log(`Moving queue ${queueId} to end of line`);
      const response = await fetch(
        `${API_BASE_URL}/partner/queues/${queueId}/move-to-end`,
        {
          method: "PUT",
          headers: {
            "Content-Type": "application/json",
          }
        }
      );

      if (!response.ok) {
        throw new Error("Failed to move queue to end of line");
      }

      const responseData = await response.json();
      console.log(`Move to end response:`, responseData);

      // Force a consistency check to ensure Redis is updated
      try {
        console.log("Forcing consistency check after moving queue to end of line");
        await fetch(
          `${API_BASE_URL}/customer/queues/ensure-consistency/${latestQueue.serviceId}?force=true`
        );

        // Wait a moment for the consistency check to complete
        await new Promise((resolve) => setTimeout(resolve, 500));
      } catch (error) {
        console.error("Error forcing consistency check:", error);
      }

      // Show success message
      Alert.alert(
        "✓ Success",
        "Queue member has been moved to the end of the line",
        [{ text: "OK" }]
      );

      // Update the UI immediately to reflect the change
      if (overview?.currentlyServing?.id === queueId) {
        // If this was the currently serving member, update the waiting members list
        // by removing this member and adding it back with updated position
        setWaitingMembers((prev) => {
          // Remove the member that was moved
          const filteredMembers = prev.filter(m => m.id !== queueId);

          // Create an updated member with the new position
          const movedMember = {
            ...overview.currentlyServing!,
            position: responseData.data.newPosition || 999999, // Use a high number as fallback
          };

          // Return the updated list
          return [...filteredMembers, movedMember];
        });

        // Clear the currently serving member
        setOverview((prev) => {
          if (!prev) return prev;
          return {
            ...prev,
            currentlyServing: null,
          };
        });
      }

      // Force an immediate refresh to update the queue data
      debouncedRefresh(true);
    } catch (error) {
      console.error("Error moving queue to end of line:", error);
      Alert.alert("Error", "Failed to move queue member to end of line");
    } finally {
      setIsNoShowLoading(false);
      closeNoShowModal();
    }
  };

  // Mark a queue as no-show - enhanced version following the verification pattern
  const markAsNoShow = async (queueId: number) => {
    setIsNoShowLoading(true);
    try {
      // First fetch the latest queue details to ensure we have the most up-to-date data
      console.log(
        `Fetching latest queue details for queue ${queueId} before marking as no-show`
      );
      const detailsResponse = await fetch(
        `${API_BASE_URL}/customer/queue/${queueId}`
      );

      if (!detailsResponse.ok) {
        throw new Error("Failed to fetch latest queue details");
      }

      const detailsData = await detailsResponse.json();
      if (detailsData.status !== "success" || !detailsData.queue) {
        throw new Error("Failed to get queue details");
      }

      const latestQueue = detailsData.queue;
      console.log(
        `Latest queue data fetched for ${queueId}, status: ${latestQueue.status}`
      );

      // Now proceed with marking as no-show
      console.log(`Marking queue ${queueId} as no-show`);
      const response = await fetch(
        `${API_BASE_URL}/partner/queues/${queueId}/no-show`,
        {
          method: "PUT",
          headers: {
            "Content-Type": "application/json",
          },
          // Include the queue ID in the body to ensure proper handling
          body: JSON.stringify({
            queueId: queueId,
            forceUpdate: true, // Add a flag to force update
          }),
        }
      );

      if (!response.ok) {
        throw new Error("Failed to mark queue as no-show");
      }

      const responseData = await response.json();
      console.log(`No-show response:`, responseData);

      // Verify the status was updated by fetching the queue again
      console.log(`Verifying status update for queue ${queueId}`);
      const verifyResponse = await fetch(
        `${API_BASE_URL}/customer/queue/${queueId}`
      );

      if (verifyResponse.ok) {
        const verifyData = await verifyResponse.json();
        if (verifyData.status === "success" && verifyData.queue) {
          console.log(
            `Verified queue ${queueId} now has status: ${verifyData.queue.status}`
          );
        }
      }

      // Show success message with checkmark icon
      Alert.alert(
        "✓ Success",
        "Queue has been marked as no-show successfully",
        [{ text: "OK" }]
      );

      // Update the UI immediately to reflect the change
      if (overview?.currentlyServing?.id === queueId) {
        // If this was the currently serving member, clear it
        setOverview((prev) => {
          if (!prev) return prev;
          return {
            ...prev,
            currentlyServing: null,
            noShows: prev.noShows + 1,
          };
        });
      }

      // Update waiting members list
      setWaitingMembers((prev) => prev.filter((m) => m.id !== queueId));

      // Add to served members with no-show status
      const noShowMember = {
        ...latestQueue,
        status: "no-show",
        fullName: latestQueue.fullName || latestQueue.userName || "Anonymous", // Ensure fullName is set
        processedAt: new Date().toISOString(), // Add timestamp for uniqueness
      };

      console.log("Adding no-show member to served list:", noShowMember);

      setServedMembers((prev) => [noShowMember, ...prev]);

      // Use debounced refresh instead of multiple refreshes
      debouncedRefresh();
    } catch (error) {
      console.error("Error marking queue as no-show:", error);
      Alert.alert("Error", "Failed to update queue status");
    } finally {
      setIsNoShowLoading(false);
      closeNoShowModal();
    }
  };

  // Start serving a queue member
  const startServing = async (queueId: number) => {
    try {
      // Get the current member
      const currentMember = overview?.currentlyServing;
      if (!currentMember) {
        Alert.alert("Error", "No current member to serve");
        setIsStartingService(false); // Reset loading state
        return;
      }

      // Open verification modal with proper setup
      openVerificationModal(currentMember, true);
    } catch (error) {
      console.error("Error preparing to start service:", error);
      Alert.alert("Error", "Failed to prepare service start");
      setIsStartingService(false); // Reset loading state
    }
  };

  // Call a queue member (make a phone call)
  const callMember = async (member: QueueMember) => {
    if (!member) {
      Alert.alert("Error", "Invalid queue member data");
      return;
    }

    // If we don't have a mobile number but we have the queue ID,
    // try one more time to fetch it directly
    if (!member.mobileNumber && member.id) {
      try {
        console.log(
          `Attempting final mobile number fetch for queue ${member.id}`
        );
        setIsLoading(true);

        const mobileResponse = await fetch(
          `${API_BASE_URL}/customer/queue-mobile/${member.id}`
        );

        if (mobileResponse.ok) {
          const mobileData = await mobileResponse.json();
          if (
            mobileData.status === "success" &&
            mobileData.data &&
            mobileData.data.mobileNumber
          ) {
            // Update the member object
            member.mobileNumber = mobileData.data.mobileNumber;
            console.log(
              `Successfully fetched mobile number: ${member.mobileNumber}`
            );
          } else {
            setIsLoading(false);
            Alert.alert(
              "No phone number",
              "Could not find a phone number for this user."
            );
            return;
          }
        }
      } catch (error) {
        console.error(`Error in final mobile number fetch: ${error}`);
      } finally {
        setIsLoading(false);
      }
    }

    if (member.mobileNumber) {
      // Format the mobile number for calling
      let phoneNumber = member.mobileNumber.trim();

      // Remove any spaces, dashes, or parentheses
      phoneNumber = phoneNumber.replace(/[\s\-\(\)]/g, "");

      // Add the "+" if it's missing and not starting with country code format
      if (!phoneNumber.startsWith("+") && !phoneNumber.startsWith("00")) {
        // If it's an Indian number (starting with 91)
        if (phoneNumber.startsWith("91") && phoneNumber.length >= 12) {
          phoneNumber = "+" + phoneNumber;
        }
        // If it's just a 10 digit number, assume it's Indian
        else if (phoneNumber.length === 10) {
          phoneNumber = "+91" + phoneNumber;
        }
      }

      console.log(`Calling member: ${phoneNumber}`);
      Linking.openURL(`tel:${phoneNumber}`);
    } else {
      Alert.alert(
        "No phone number",
        "This user does not have a registered phone number."
      );
    }
  };

  // Filter members based on search query and exclude currently serving member from waiting list
  const filteredMembers = useCallback(() => {
    let members = activeTab === "waiting" ? waitingMembers : servedMembers;

    // If we're on the waiting tab and have a currently serving member, exclude them from the list
    if (activeTab === "waiting" && overview?.currentlyServing) {
      members = members.filter(
        (member) => member.id !== overview.currentlyServing?.id
      );

      // No need to sort here, the members are already sorted when they're loaded
    }

    if (!searchQuery.trim()) {
      return members;
    }

    const query = searchQuery.toLowerCase();
    return members.filter((member) =>
      member.fullName?.toLowerCase().includes(query)
    );
  }, [
    activeTab,
    waitingMembers,
    servedMembers,
    searchQuery,
    overview?.currentlyServing,
  ]);

  // Add these functions to handle grace period

  /**
   * Start the grace period for the currently serving member
   */
  const startGracePeriod = async (queueId: number) => {
    try {
      console.log(`Starting grace period for queue ${queueId}`);
      setIsMarkServedLoading(true);

      // Call the API to start grace period
      const response = await fetch(
        `${API_BASE_URL}/partner/queues/${queueId}/start-grace-period`,
        {
          method: "PUT",
          headers: {
            "Content-Type": "application/json",
          },
        }
      );

      if (!response.ok) {
        throw new Error(`Failed to start grace period: ${response.status}`);
      }

      const data = await response.json();
      console.log("Grace period started:", data);

      // Update state to show grace period UI
      setInGracePeriod(true);

      // Start polling for grace period status
      startGracePeriodPolling(queueId);

      // Refresh queue data
      performFastDataRefresh();
    } catch (error) {
      console.error("Error starting grace period:", error);
      Alert.alert("Error", "Failed to start grace period. Please try again.");
    } finally {
      setIsMarkServedLoading(false);
    }
  };

  /**
   * Poll for grace period status updates
   */
  const startGracePeriodPolling = (queueId: number | undefined) => {
    // Clear any existing timer
    if (gracePeriodTimer) {
      clearInterval(gracePeriodTimer);
      setGracePeriodTimer(null);
    }

    // Exit if no queue ID
    if (!queueId) return;

    // First, get the initial grace period status
    fetchGracePeriodStatus(queueId)
      .then((status) => {
        if (!isMounted.current) return;

        // Update state with initial values
        setGracePeriodStatus(status);
        setInGracePeriod(status.inGracePeriod);
        setRemainingGraceTime(status.remainingSeconds);

        // If grace period is already over, don't start countdown
        if (!status.inGracePeriod || status.confirmedPresence || status.isExpired) {
          if (status.isExpired && !status.confirmedPresence) {
            handleGracePeriodExpired(queueId);
          }
          return;
        }

        // Start the local countdown timer (updates every second)
    const timer = setInterval(() => {
      if (!isMounted.current) {
        clearInterval(timer);
        return;
      }

          // Update the remaining time locally
          setRemainingGraceTime((prev) => {
            // If time is up, clear timer and mark as expired
            if (prev <= 1) {
              clearInterval(timer);
              setGracePeriodTimer(null);

              // Important: Don't set inGracePeriod to false here
              // Let handleGracePeriodExpired handle all state updates
              // This prevents race conditions

              // Handle expiration - this will reset UI state and refresh data
              handleGracePeriodExpired(queueId);
              return 0;
            }
            return prev - 1;
          });
        }, 1000); // Update every second

        // Set the timer reference for cleanup
        setGracePeriodTimer(timer);

        // Also set up a background sync every 15 seconds to ensure we're still in sync with backend
        // but not for the countdown display
        const syncTimer = setInterval(() => {
          if (!isMounted.current) {
            clearInterval(syncTimer);
            return;
          }

          // Only check status for confirmation changes, not for time display
    fetchGracePeriodStatus(queueId)
            .then((newStatus) => {
              if (!isMounted.current) return;

              // If the backend status has changed (confirmed or expired), update accordingly
              if (!newStatus.inGracePeriod || newStatus.confirmedPresence || newStatus.isExpired) {
                // Clear timers
                clearInterval(timer);
                clearInterval(syncTimer);
                setGracePeriodTimer(null);

                // If the customer confirmed in the app
                if (newStatus.confirmedPresence) {
                  console.log("Customer confirmed arrival in the app");
                  // Set UI state first
                  setInGracePeriod(false);
                  // Then refresh data
                  fetchQueueData(true);
                }
                // If the backend marked as expired
                else if (newStatus.isExpired && !newStatus.confirmedPresence) {
                  // Let handleGracePeriodExpired handle all state updates and data refresh
                  handleGracePeriodExpired(queueId);
                }
                // For any other case where grace period ended
                else {
                  // Set UI state first
                  setInGracePeriod(false);
                  // Then refresh data
                  fetchQueueData(true);
                }
              }
            })
            .catch((error) => {
              console.error("Error in background sync:", error);
            });
        }, 15000); // Sync with backend every 15 seconds
      })
      .catch((error) => {
        console.error("Failed to initiate grace period timer:", error);
        if (isMounted.current) {
          setInGracePeriod(false);
        }
      });
  };

  /**
   * Fetch the current grace period status
   */
  const fetchGracePeriodStatus = async (
    queueId: number | undefined
  ): Promise<GracePeriodStatus> => {
    if (!queueId) {
      console.error("Cannot fetch grace period status: queueId is undefined");
      return {
        inGracePeriod: false,
        remainingSeconds: 0,
        confirmedPresence: false,
        isExpired: false,
        queueId: 0,
        graceStartedAt: new Date().toISOString(),
        graceTimeSeconds: 0,
        status: "not_started",
      };
    }

    const response = await fetch(
      `${API_BASE_URL}/partner/queues/${queueId}/grace-period-status`
    );

    if (!response.ok) {
      throw new Error(`Failed to get grace period status: ${response.status}`);
    }

    const data = await response.json();
    return data.data;
  };

  /**
   * Format the remaining time in grace period as MM:SS
   */
  const formatGraceTimeRemaining = (seconds: number): string => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes.toString().padStart(2, "0")}:${remainingSeconds.toString().padStart(2, "0")}`;
  };

  // Add clean up effect
  useEffect(() => {
    // Clean up interval on unmount
    return () => {
      if (gracePeriodTimer) {
        clearInterval(gracePeriodTimer);
      }
      if (servingTimer) {
        clearInterval(servingTimer);
      }
    };
  }, [gracePeriodTimer, servingTimer]);

  // Using the existing formatGraceTimeRemaining function for time formatting

  /**
   * Check if a customer is currently being served
   */
  const checkServingStatus = async (queueId: number | undefined) => {
    if (!queueId) {
      console.error("Cannot check serving status: queueId is undefined");
      return;
    }

    try {
      // First get the queue details to check if it has subunits
      const queueResponse = await fetch(
        `${API_BASE_URL}/customer/queue/${queueId}`
      );

      if (!queueResponse.ok) {
        throw new Error(`Failed to get queue details: ${queueResponse.status}`);
      }

      const queueData = await queueResponse.json();
      const hasSubUnits = queueData.queue?.hasSubUnits || false;
      const subUnitId = queueData.queue?.subUnitId || "0";

      console.log(`Queue ${queueId} hasSubUnits: ${hasSubUnits}, subUnitId: ${subUnitId}`);

      // Now get the serving status with the subunit information
      const response = await fetch(
        `${API_BASE_URL}/partner/queues/${queueId}/serving-status?hasSubUnits=${hasSubUnits}&subUnitId=${subUnitId}`
      );

      if (!response.ok) {
        throw new Error(`Failed to get serving status: ${response.status}`);
      }

      const data = await response.json();
      if (data.status === "success" && data.data.isServing) {
        console.log(`Serving status for queue ${queueId}:`, data.data);
        console.log(`Serving time: ${data.data.servingTimeMinutes} minutes`);

        // Store the serving status data
        setServingStatus(data.data);

        // Store the remaining time
        setRemainingServingTime(data.data.remainingSeconds);

        // Store the initial serving time for overtime calculation
        setInitialServingTime(data.data.servingTimeMinutes * 60);

        // Store the serving start time - make sure it's not null
        if (data.data.servingStartedAt) {
          console.log(`Setting servingStartedAt to: ${data.data.servingStartedAt}`);
          setServingStartedAt(data.data.servingStartedAt);
        } else {
          console.warn(`No servingStartedAt in response, using current time`);
          // If no servingStartedAt is provided, use current time minus the elapsed time
          const now = new Date();
          const elapsedSeconds = data.data.servingTimeMinutes * 60 - data.data.remainingSeconds;
          const estimatedStartTime = new Date(now.getTime() - (elapsedSeconds * 1000));
          console.log(`Estimated start time: ${estimatedStartTime.toISOString()}`);
          setServingStartedAt(estimatedStartTime.toISOString());
        }

        // Start timer for countdown
        startServingTimer(data.data.queueId);
      } else {
        // Not serving anyone
        setServingStatus(null);
        if (servingTimer) {
          clearInterval(servingTimer);
          setServingTimer(null);
        }
      }
    } catch (error) {
      console.error("Error checking serving status:", error);
    }
  };

  /**
   * Start timer for serving countdown
   */
  const startServingTimer = (queueId: number | undefined) => {
    // If queueId is undefined, don't start timer
    if (!queueId) {
      console.error("Cannot start serving timer: queueId is undefined");
      return;
    }

    // Clear any existing timer
    if (servingTimer) {
      clearInterval(servingTimer);
      setServingTimer(null);
    }

    // Create a new timer that updates every second for real-time countdown
    const timer = setInterval(() => {
      setRemainingServingTime((prev) => {
        // Always decrement by 1, whether positive or negative
        // This ensures we track overtime correctly when prev is negative
        const newValue = prev - 1;
        console.log(`Timer update: ${prev} -> ${newValue}`);
        return newValue;
      });

      // Sync with backend every 10 seconds to ensure we're still in sync
      if (remainingServingTime % 10 === 0 && remainingServingTime > 0) {
        // Check serving status to sync time
        fetch(`${API_BASE_URL}/partner/queues/${queueId}/serving-status`)
          .then((response) => {
            if (!response.ok) {
              throw new Error(
                `Failed to sync serving status: ${response.status}`
              );
            }
            return response.json();
          })
          .then((data) => {
            if (data.status === "success") {
              const status = data.data;

              // If no longer serving, stop the timer and refresh
              if (!status.isServing) {
                clearInterval(timer);
                setServingTimer(null);
                setServingStatus(null);
                fetchQueueData(); // Use full data refresh instead
                return;
              }

              // Only update remaining time if it's significantly different
              // This prevents jumps in the timer display
              if (
                Math.abs(status.remainingSeconds - remainingServingTime) > 3
              ) {
                setRemainingServingTime(status.remainingSeconds);
              }
            }
          })
          .catch((err: Error) => {
            console.error("Error syncing serving status:", err);
          });
      }
    }, 1000); // Update every second for real-time countdown

    setServingTimer(timer);
  };

  /**
   * Mark service as completed
   */
  const completeService = async (queueId: number) => {
    try {
      setIsMarkServedLoading(true);

      // We're completing service (no verification needed)
      setIsStartingService(false);

      // Get the current member
      const currentMember = overview?.currentlyServing;
      if (!currentMember) {
        Alert.alert("Error", "No current member to complete");
        return;
      }

      // Call the complete endpoint directly without verification
      markAsCompleted(queueId);
    } catch (error) {
      console.error("Error completing service:", error);
      Alert.alert("Error", "Failed to complete service");
    } finally {
      setIsMarkServedLoading(false);
    }
  };

  // Add polling for grace period and serving status - modified to respect component mount state
  useEffect(() => {
    if (!overview?.currentlyServing || !isMounted.current) return;

      console.log(
        `Checking status for current queue member (ID: ${overview?.currentlyServing?.id})`
      );

      // One-time check (not with polling) for grace period and serving status
      if (overview?.currentlyServing?.id) {
        // Check if member has status 'serving' - if so, check serving status
        if (overview.currentlyServing.status === "serving" && !servingTimer) {
          checkServingStatus(overview.currentlyServing.id);
        }

        // Only check grace period if not already polling
        if (!gracePeriodTimer) {
          fetchGracePeriodStatus(overview?.currentlyServing?.id)
            .then((status) => {
            if (!isMounted.current) return;

              if (status.inGracePeriod) {
                console.log(
                  `Queue ${overview?.currentlyServing?.id} is in grace period`
                );
                setInGracePeriod(true);
                setGracePeriodStatus(status);
                setRemainingGraceTime(status.remainingSeconds);
                startGracePeriodPolling(overview?.currentlyServing?.id);
              } else if (status.confirmedPresence) {
                console.log(
                  `Queue ${overview?.currentlyServing?.id} has confirmed presence`
                );
                setInGracePeriod(false);
                // No need to refresh data here
              }
            })
            .catch((error) => {
              console.error(`Error checking grace period status: ${error}`);
            });
        }
      }

      // Set up polling interval for regular status checks - once every 10 seconds is sufficient
      const statusCheckInterval = setInterval(() => {
      if (!isMounted.current) {
        clearInterval(statusCheckInterval);
        return;
      }

        if (overview?.currentlyServing?.id) {
          console.log(
            `Polling for status updates for queue ${overview?.currentlyServing?.id}`
          );

          // Check if member has status 'serving' - if so, check serving status
          if (overview.currentlyServing.status === "serving" && !servingTimer) {
            checkServingStatus(overview.currentlyServing.id);
          }

          // Only check grace period if not already polling
          if (!gracePeriodTimer) {
            fetchGracePeriodStatus(overview?.currentlyServing?.id)
              .then((status) => {
              if (!isMounted.current) return;

                if (status.inGracePeriod) {
                  setInGracePeriod(true);
                  setGracePeriodStatus(status);
                  setRemainingGraceTime(status.remainingSeconds);
                  startGracePeriodPolling(overview?.currentlyServing?.id);
                }
              })
              .catch((error) => {
                console.error(`Error polling grace period status: ${error}`);
              });
          }
        }
    }, 10000); // Check every 10 seconds for better responsiveness

      return () => {
        clearInterval(statusCheckInterval);
      };
  }, [overview?.currentlyServing]);

  // Add function to handle grace period expiration in the partner app too
  const handleGracePeriodExpired = async (queueId: number) => {
    try {
      console.log(`Grace period expired for queue ${queueId}, marking as no-show immediately`);

      // Reset UI state immediately to prevent showing expired grace period UI
      setInGracePeriod(false);
      setGracePeriodStatus(null);
      setRemainingGraceTime(0);

      // Clear any existing timers
      if (gracePeriodTimer) {
        clearInterval(gracePeriodTimer);
        setGracePeriodTimer(null);
      }

      // Call the backend API to mark as no-show immediately
      const response = await fetch(
        `${API_BASE_URL}/customer/queue/${queueId}/grace-period-expired`,
        {
          method: "PUT",
          headers: {
            "Content-Type": "application/json",
          }
        }
      );

      if (!response.ok) {
        console.error(`Failed to mark grace period as expired: ${response.status}`);
      } else {
        console.log("Successfully marked grace period as expired");

        // Force a full data refresh with state reset to ensure UI is updated correctly
        await fetchQueueData(true);
      }
    } catch (error) {
      console.error("Error marking grace period as expired:", error);
      // Even if there's an error, force a refresh to update the UI
      await fetchQueueData(true);
    }
  };

  // Get status color based on the queue status
  const getStatusColor = () => {
    switch (status) {
      case "ongoing":
        return { text: "text-primary-500" };
      case "ended":
        return { text: "text-secondary-600" };
      case "not-started":
        return { text: "text-warning-500" };
      default:
        return { text: "text-primary-500" };
    }
  };

  // Render a single queue member item
  const renderMemberItem = ({ item }: { item: QueueMember }) => {
    // Check if it's in the waiting or served tab
    const isWaiting = activeTab === "waiting";

    console.log(
      `Rendering member ${item.id} with status: ${item.status}, isCheckedIn: ${item.isCheckedIn}`
    );

    return (
      <View className="flex-row justify-between items-center bg-white rounded-3xl p-6 mb-6 border border-gray-200">
        <View className="flex-col items-start">
          <View>
            <View className="flex-row items-center mb-3">
              <Text className="font-poppins-medium text-secondary-800 text-base">
                {item.fullName || "Anonymous"}
              </Text>
              {item.isVIP && (
                <View className="ml-2 bg-warning-500 rounded-lg px-2 py-1 flex-row items-center">
                  <Image
                    source={images.vip}
                    className="w-3 h-3 mr-1"
                    tintColor="#fffff"
                  />
                  <Text className="text-white text-xs font-poppins-medium">
                    VIP
                  </Text>
                </View>
              )}
            </View>
          </View>

          {/* Check-in status display for waiting tab */}
          {isWaiting && item.isCheckedIn && (
            <View className="bg-green-500/10 rounded-lg px-3 py-1">
              <Text className="text-success-600 text-xs font-poppins-medium">
                Arrived
              </Text>
            </View>
          )}
          {isWaiting && !item.isCheckedIn && (
            <View className="bg-gray-200 rounded-lg px-3 py-1">
              <Text className="text-gray-600 text-xs font-poppins-medium">
                Not Arrived
              </Text>
            </View>
          )}

          {/* Show status for served members tab */}
          {!isWaiting && item.status === "no-show" && (
            <View className="flex-row items-center">
              <View className="bg-danger-600/10 px-3 py-1 rounded-lg mr-2">
                <Text className="text-danger-600 text-xs font-poppins-medium">
                  No Show
                </Text>
              </View>
            </View>
          )}

          {!isWaiting && item.status === "completed" && (
            <View className="flex-row items-center">
              <View className="bg-success-600/10 px-3 py-1 rounded-lg mr-2">
                <Text className="text-success-600 text-xs font-poppins-medium">
                  Completed
                </Text>
              </View>
            </View>
          )}
        </View>

        {/* Call button only for waiting tab */}
        {isWaiting && (
          <TouchableOpacity
            className="h-12 w-12 rounded-full flex-row justify-center items-center border border-primary-500"
            onPress={() => {
              // Handle the async callMember correctly
              callMember(item).catch((error) => {
                console.error("Error calling member:", error);
                Alert.alert("Error", "Failed to make the call");
              });
            }}
          >
            <Image
              source={images.call}
              className="w-5 h-5"
              tintColor="#159AFF"
            />
          </TouchableOpacity>
        )}
        {!isWaiting && item.statusUpdatedAt && (
          <View className="flex-row items-center">
            <Image
              source={images.clock}
              className="w-5 h-5 mr-2"
              tintColor={"#62727A"}
            />
            <Text className="text-secondary-600 text-base font-poppins-medium text-center uppercase">
              {new Date(item.statusUpdatedAt).toLocaleTimeString([], {
                hour: "2-digit",
                minute: "2-digit",
                hour12: true,
              })}
            </Text>
          </View>
        )}
      </View>
    );
  };

  // Render currently serving member UI with better styling and actions
  const renderCurrentlyServingActions = () => {
    if (!overview?.currentlyServing) return null;

    // If we're already showing serving status, don't show the old serving card
    if (
      servingStatus?.isServing ||
      overview.currentlyServing.status === "serving"
    ) {
      return (
        <View className="mt-4 flex-col  p-6 rounded-3xl border-2 border-primary-500/70">
          <View className="w-full flex-row justify-between items-center mb-6">
            <View className="flex-col items-start">
              <View className="flex-row items-center mb-2">
                <Text className="font-poppins-medium text-lg">
                  {overview.currentlyServing.fullName || "Anonymous"}
                </Text>
                {overview.currentlyServing.isVIP && (
                  <View className="ml-2 bg-warning-500 rounded-lg px-2 py-1 flex-row items-center">
                    <Image
                      source={images.vip}
                      className="w-3 h-3 mr-1"
                      tintColor="#ffffff"
                    />
                    <Text className="text-white text-xs font-poppins-medium">
                      VIP
                    </Text>
                  </View>
                )}
              </View>
              {overview.currentlyServing.isCheckedIn ? (
                <View className="bg-success-500/10 rounded-lg px-2 py-1 mr-2">
                  <Text className="text-success-500 font-poppins-medium  text-xs">
                    Arrived
                  </Text>
                </View>
              ) : (
                <View className="bg-gray-200 rounded-lg px-2 py-1 mr-2">
                  <Text className="text-gray-600 font-poppins-medium  text-xs">
                    Not Arrived
                  </Text>
                </View>
              )}
            </View>
            <TouchableOpacity
              className="h-12 w-12 rounded-full flex-row border border-primary-500 justify-center items-center "
              onPress={() => {
                // Handle the async callMember correctly
                if (overview.currentlyServing) {
                  callMember(overview.currentlyServing).catch((error) => {
                    console.error("Error calling member:", error);
                    Alert.alert("Error", "Failed to make the call");
                  });
                }
              }}
            >
              <Image
                source={images.call}
                className="w-5 h-5"
                tintColor="#159AFF"
              />
            </TouchableOpacity>
          </View>

          <View className="flex-row w-full justify-center items-center mb-5">
            <View className="">
              {(() => {
                // Get service time from service details
                const serviceTime = serviceDetails?.queueInfo?.servingTime || 15;
                const serviceTimeSeconds = serviceTime * 60;

                // Calculate if we're in overtime based on real-world time
                let isOvertime = false;
                let overtimeSeconds = 0;

                if (servingStartedAt) {
                  // Calculate based on actual start time and current time
                  const startTime = new Date(servingStartedAt).getTime();
                  const currentTime = new Date().getTime();
                  const elapsedMilliseconds = currentTime - startTime;
                  const elapsedSeconds = Math.floor(elapsedMilliseconds / 1000);

                  // Check if we've exceeded the service time
                  isOvertime = elapsedSeconds > serviceTimeSeconds;

                  if (isOvertime) {
                    // Calculate how many seconds over the service time
                    overtimeSeconds = elapsedSeconds - serviceTimeSeconds;
                  }
                } else {
                  // Fallback to the old calculation if servingStartedAt is not available
                  isOvertime = remainingServingTime <= 0 || (initialServingTime > 0 && initialServingTime - remainingServingTime > serviceTimeSeconds);

                  if (isOvertime) {
                    // Calculate overtime seconds
                    if (remainingServingTime <= 0) {
                      // If countdown is negative, use the absolute value
                      overtimeSeconds = Math.abs(remainingServingTime);
                    } else {
                      // Otherwise calculate from the difference between initial and current
                      overtimeSeconds = (initialServingTime - remainingServingTime) - serviceTimeSeconds;
                    }
                  }
                }

                if (isOvertime) {
                  // Format the overtime in hh:mm:ss format
                  const overtimeHours = Math.floor(overtimeSeconds / 3600);
                  const overtimeMinutes = Math.floor((overtimeSeconds % 3600) / 60);
                  const overtimeRemainingSeconds = overtimeSeconds % 60;

                  return (
                    <View>
                      <Text className="text-danger-600 text-sm font-poppins-medium">
                        Extra Serve time: {overtimeHours.toString().padStart(2, "0")}:{overtimeMinutes.toString().padStart(2, "0")}:{overtimeRemainingSeconds.toString().padStart(2, "0")}
                      </Text>
                    </View>
                  );
                } else {
                  // Normal countdown display in hh:mm:ss format
                  const hours = Math.floor(remainingServingTime / 3600);
                  const minutes = Math.floor((remainingServingTime % 3600) / 60);
                  const seconds = remainingServingTime % 60;

                  return (
                    <Text className="text-secondary-600 text-sm font-poppins-medium">
                      Time Remaining: {hours.toString().padStart(2, "0")}:{minutes.toString().padStart(2, "0")}:{seconds.toString().padStart(2, "0")}
                    </Text>
                  );
                }
              })()}
            </View>
          </View>

          <TouchableOpacity
            className={`bg-primary-500 w-full flex-row justify-center items-center rounded-xl p-4 ${
              isMarkServedLoading ? "opacity-70" : ""
            }`}
            onPress={() =>
              overview.currentlyServing &&
              markAsCompleted(overview.currentlyServing.id)
            }
            disabled={isMarkServedLoading}
          >
            {isMarkServedLoading ? (
              <ActivityIndicator size="small" color="#ffffff" />
            ) : (
              <>
                <Image source={images.check} className="w-4 h-3 mr-3" />
                <Text className="text-white font-poppins-medium">
                  Mark as Served
                </Text>
              </>
            )}
          </TouchableOpacity>
        </View>
      );
    }

    // Show grace period UI
    if (inGracePeriod) {
      return (
        <View className="mt-4 flex-col  p-6 rounded-3xl border-2 border-primary-500/70">
          <View className="w-full flex-row justify-between items-center mb-6">
            <View className="flex-col items-start">
            <View className="flex-row items-center mb-2">
                <Text className="font-poppins-medium text-lg">
                  {overview.currentlyServing.fullName || "Anonymous"}
                </Text>
                {overview.currentlyServing.isVIP && (
                  <View className="ml-2 bg-warning-500 rounded-lg px-2 py-1 flex-row items-center">
                    <Image
                      source={images.vip}
                      className="w-3 h-3 mr-1"
                      tintColor="#ffffff"
                    />
                    <Text className="text-white text-xs font-poppins-medium">
                      VIP
                    </Text>
                  </View>
                )}
              </View>
              {overview.currentlyServing.isCheckedIn ? (
                <View className="bg-success-500/10 rounded-lg px-2 py-1 mr-2">
                  <Text className="text-success-500 font-poppins-medium  text-xs">
                    Arrived
                  </Text>
                </View>
              ) : (
                <View className="bg-gray-200 rounded-lg px-2 py-1 mr-2">
                  <Text className="text-gray-600 font-poppins-medium  text-xs">
                    Not Arrived
                  </Text>
                </View>
              )}
            </View>
            <TouchableOpacity
              className="h-12 w-12 rounded-full flex-row border border-primary-500 justify-center items-center "
              onPress={() => {
                // Handle the async callMember correctly
                if (overview.currentlyServing) {
                  callMember(overview.currentlyServing).catch((error) => {
                    console.error("Error calling member:", error);
                    Alert.alert("Error", "Failed to make the call");
                  });
                }
              }}
            >
              <Image
                source={images.call}
                className="w-5 h-5"
                tintColor="#159AFF"
              />
            </TouchableOpacity>
          </View>
          <View className="flex-row justify-start bg-primary-500/5 border p-4 rounded-2xl border-primary-500/20 items-center">
            <View className="p-2 px-[10px] border-2 border-primary-500 mr-3 rounded-xl">
              <Image
                source={images.clock}
                className="w-7 h-7  mb-1"
                tintColor={"#159AFF"}
              />
            </View>
            <View className="flex-col justify-start items-start">
              <Text className="font-poppins-medium text-primary-500">
                Waiting for Confirmation
              </Text>
              <Text className="font-poppins-regular text-sm text-secondary-600">
                Gracetime: {formatGraceTimeRemaining(remainingGraceTime)}
              </Text>
            </View>
          </View>
        </View>
      );
    }

    // Default case - Show check-in status and actions
    return (
      <View className="mt-4  p-6 rounded-3xl border-2 border-primary-500/70">
        <View className="w-full flex-row justify-between items-center mb-6">
          <View className="flex-col items-start">
          <View className="flex-row items-center mb-2">
                <Text className="font-poppins-medium text-lg">
                  {overview.currentlyServing.fullName || "Anonymous"}
                </Text>
                {overview.currentlyServing.isVIP && (
                  <View className="ml-2 bg-warning-500 rounded-lg px-2 py-1 flex-row items-center">
                    <Image
                      source={images.vip}
                      className="w-3 h-3 mr-1"
                      tintColor="#ffffff"
                    />
                    <Text className="text-white text-xs font-poppins-medium">
                      VIP
                    </Text>
                  </View>
                )}
              </View>
            {overview.currentlyServing.isCheckedIn ? (
              <View className="bg-success-500/10 rounded-lg px-2 py-1 mr-2">
                <Text className="text-success-500 font-poppins-medium  text-xs">
                  Arrived
                </Text>
              </View>
            ) : (
              <View className="bg-gray-200 rounded-lg px-2 py-1 mr-2">
                <Text className="text-gray-600 font-poppins-medium  text-xs">
                  Not Arrived
                </Text>
              </View>
            )}
          </View>
          <TouchableOpacity
            className="h-12 w-12 rounded-full flex-row border border-primary-500 justify-center items-center "
            onPress={() => {
              // Handle the async callMember correctly
              if (overview.currentlyServing) {
                callMember(overview.currentlyServing).catch((error) => {
                  console.error("Error calling member:", error);
                  Alert.alert("Error", "Failed to make the call");
                });
              }
            }}
          >
            <Image
              source={images.call}
              className="w-5 h-5"
              tintColor="#159AFF"
            />
          </TouchableOpacity>
        </View>

        {/* Action Buttons based on check-in status */}
        <View className="flex-row justify-between space-x-4">
          {overview.currentlyServing.isCheckedIn ? (
            <>
              <TouchableOpacity
                className={`bg-danger-500 w-[47%] flex-row justify-center items-center rounded-xl p-4 ${
                  isNoShowLoading ? "opacity-70" : ""
                }`}
                onPress={() =>
                  overview.currentlyServing &&
                  markAsNoShow(overview.currentlyServing.id)
                }
                disabled={isNoShowLoading}
              >
                {isNoShowLoading ? (
                  <ActivityIndicator size="small" color="#ffffff" />
                ) : (
                  <>
                    <Image
                      source={images.cross}
                      className="w-[13px] h-[13px] mr-3"
                      tintColor={"#ffff"}
                    />
                    <Text className="text-white font-poppins-medium">
                      Not Here
                    </Text>
                  </>
                )}
              </TouchableOpacity>
              <TouchableOpacity
                className={`bg-[#10B981] w-[47%] flex-row justify-center items-center rounded-xl p-4 ${
                  isStartingService ? "opacity-70" : ""
                }`}
                onPress={() =>
                  overview.currentlyServing &&
                  startServing(overview.currentlyServing.id)
                }
                disabled={isStartingService}
              >
                {isStartingService ? (
                  <ActivityIndicator size="small" color="#ffffff" />
                ) : (
                  <>
                    <Image
                      source={images.play}
                      className="w-[14px] h-[15px] mr-2"
                    />
                    <Text className="text-white font-poppins-medium">
                      Start Serving
                    </Text>
                  </>
                )}
              </TouchableOpacity>
            </>
          ) : (
            <TouchableOpacity
              className={`bg-warning-500 w-full flex-row justify-center items-center rounded-xl p-4 ${
                isMarkServedLoading ? "opacity-70" : ""
              }`}
              onPress={() =>
                overview.currentlyServing &&
                startGracePeriod(overview.currentlyServing.id)
              }
              disabled={isMarkServedLoading}
            >
              {isMarkServedLoading ? (
                <ActivityIndicator size="small" color="#ffffff" />
              ) : (
                <>
                  <Image
                    source={images.clock}
                    className="w-5 h-5 mr-3"
                    tintColor={"#ffff"}
                  />
                  <Text className="text-white font-poppins-medium">
                    Start Grace Period
                  </Text>
                </>
              )}
            </TouchableOpacity>
          )}
        </View>
      </View>
    );
  };

  // Modified function to handle digit-by-digit input
  const handleDigitChange = (index: number, value: string) => {
    // Reset validation state when user starts typing
    if (invalidInput) {
      setInvalidInput(false);
    }

    // Handle empty value (deletion) - add special handling for this case
    if (value === '') {
      const newCode = verificationSlotId.split('');
      newCode[index] = '';
      setVerificationSlotId(newCode.join(''));
      return;
    }

    if (value.length > 1) {
      // Handle paste
      const pastedCode = value.slice(0, 4);

      // Only accept numeric values
      if (!/^\d+$/.test(pastedCode)) {
        setInvalidInput(true);
        setVerificationSlotId("");
        setTimeout(() => {
          inputRefs.current[0]?.focus?.();
        }, 10);
        return;
      }

      setVerificationSlotId(pastedCode);

      // Focus last input if complete
      if (pastedCode.length === 4 && inputRefs.current[3]) {
        setTimeout(() => {
          inputRefs.current[3]?.focus?.();
        }, 10);
      }
      } else {
      // Only allow numeric input
      if (value && !/^[0-9]$/.test(value)) {
        setInvalidInput(true);

        // Clear all inputs on invalid entry
        setVerificationSlotId("");

        // Return focus to first input with a slight delay
        setTimeout(() => {
          inputRefs.current[0]?.focus?.();
        }, 10);
        return;
      }

      // Handle single character input
      const newCode = verificationSlotId.split('');
      newCode[index] = value;
      setVerificationSlotId(newCode.join(''));

      // Move focus forward with a slight delay
      if (value && index < 3) {
        setTimeout(() => {
          inputRefs.current[index + 1]?.focus?.();
        }, 10);
      }
    }
  };

  // Reset verification modal state when opening
  const openVerificationModal = (member: QueueMember, isStarting: boolean) => {
    // Clear any previous state
    setVerificationSlotId("");
    setVerificationError("");
    setInvalidInput(false);
    setSelectedQueueMember(member);
    setIsStartingService(isStarting);

    // Set modal visible
    setVerificationModalVisible(true);

    // Focus the first input with a slight delay
    setTimeout(() => {
      if (inputRefs.current[0]) {
        inputRefs.current[0].focus();
      }
    }, 10);
  };

  // Handle modal animations
  useEffect(() => {
    if (verificationModalVisible) {
      // Fade in and scale up when modal opens
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 300,
          easing: Easing.bezier(0.25, 0.1, 0.25, 1),
          useNativeDriver: true,
        }),
        Animated.timing(scaleAnim, {
          toValue: 1,
          duration: 300,
          easing: Easing.bezier(0.25, 0.1, 0.25, 1),
          useNativeDriver: true,
        }),
      ]).start();
    } else {
      // Reset animations when modal is closed
      fadeAnim.setValue(0);
      scaleAnim.setValue(0.9);
    }
  }, [verificationModalVisible, fadeAnim, scaleAnim]);

  // Add a useEffect to reset states when currentlyServing ID changes
  useEffect(() => {
    // Get the current member ID
    const currentMemberId = overview?.currentlyServing?.id;

    console.log(`Currently serving member changed to: ${currentMemberId || 'None'}`);

    // Always reset all service and grace period states when the current member changes
    // This ensures that when a new person becomes the currently serving member,
    // they start with a clean slate (no leftover timers, grace periods, or serving status from previous person)

    console.log("Resetting all states for new currently serving member");

    // Clear grace period state
    setInGracePeriod(false);
    setGracePeriodStatus(null);
    setRemainingGraceTime(120); // Reset to default 2 minutes

    // Clear serving state
    setServingStatus(null);
    setRemainingServingTime(0);
    setInitialServingTime(0);
    setServingStartedAt(null);

    // Clear loading states
    setIsStartingService(false);
    setIsMarkServedLoading(false);
    setIsNoShowLoading(false);

    // Clear timers
    if (gracePeriodTimer) {
      clearInterval(gracePeriodTimer);
      setGracePeriodTimer(null);
      console.log("Cleared grace period timer");
    }

    if (servingTimer) {
      clearInterval(servingTimer);
      setServingTimer(null);
      console.log("Cleared serving timer");
    }

    // If there's a new member, log their details
    if (currentMemberId) {
      console.log(`New member ${currentMemberId} is now being served - all states reset`);
    } else {
      console.log("No member currently being served - all states cleared");
    }
  }, [overview?.currentlyServing?.id]);

  // Add key-based reset to force remount when queue ID changes
  const ServingActionsCard = () => {
    // Render current actions based on member state
    if (!overview?.currentlyServing) return null;

    const currentMember = overview.currentlyServing;
    const memberStatus = currentMember.status;
    const isCheckedIn = !!currentMember.isCheckedIn;

    // Determine which UI to show based on clear conditions
    const isServing = memberStatus === "serving" || servingStatus?.isServing;

    // Show timer UI if member is being served
    if (isServing) {
      return (
        <View className="mt-4 flex-col p-6 rounded-3xl border-2 border-primary-500/70">
          <View className="w-full flex-row justify-between items-center mb-6">
            <View className="flex-col items-start">
              <View className="flex-row items-center mb-2">
                <Text className="font-poppins-medium text-lg">
                  {currentMember.fullName || "Anonymous"}
                </Text>
                {currentMember.isVIP && (
                  <View className="ml-2 bg-warning-500 rounded-lg px-2 py-1 flex-row items-center">
                    <Image
                      source={images.vip}
                      className="w-3 h-3 mr-1"
                      tintColor="#ffffff"
                    />
                    <Text className="text-white text-xs font-poppins-medium">
                      VIP
                    </Text>
                  </View>
                )}
              </View>
              <View className="bg-success-500/10 rounded-lg px-2 py-1 mr-2">
                <Text className="text-success-500 font-poppins-medium text-xs">
                  Serving
                </Text>
              </View>
            </View>
            <TouchableOpacity
              className="h-12 w-12 rounded-full flex-row border border-primary-500 justify-center items-center"
              onPress={() => {
                if (currentMember) {
                  callMember(currentMember).catch((error) => {
                    console.error("Error calling member:", error);
                    Alert.alert("Error", "Failed to make the call");
                  });
                }
              }}
            >
              <Image
                source={images.call}
                className="w-5 h-5"
                tintColor="#159AFF"
              />
            </TouchableOpacity>
          </View>

          <View className="flex-row w-full justify-center items-center mb-5">
            <View className="">
              {(() => {
                // Get service time from service details or from the queue's serveTime
                // First try to get it from servingStatus which has the most accurate data
                const serviceTime = servingStatus?.servingTimeMinutes ||
                                   currentMember?.serveTime ||
                                   serviceDetails?.queueInfo?.servingTime ||
                                   5; // Default to 5 minutes if all else fails
                const serviceTimeSeconds = serviceTime * 60;

                console.log(`Using service time: ${serviceTime} minutes (${serviceTimeSeconds} seconds)`);
                console.log(`Service time sources: servingStatus.servingTimeMinutes=${servingStatus?.servingTimeMinutes}, currentMember.serveTime=${currentMember?.serveTime}, serviceDetails.queueInfo.servingTime=${serviceDetails?.queueInfo?.servingTime}`);

                // Calculate if we're in overtime based on real-world time
                let isOvertime = false;
                let overtimeSeconds = 0;

                // Debug the current time values
                console.log(`Debug - Current time values:`);
                console.log(`serviceTime: ${serviceTime}`);
                console.log(`serviceTimeSeconds: ${serviceTimeSeconds}`);
                console.log(`remainingServingTime: ${remainingServingTime}`);
                console.log(`initialServingTime: ${initialServingTime}`);

                // First check if we have servingStartedAt from servingStatus (most accurate)
                const effectiveServingStartedAt = servingStatus?.servingStartedAt || servingStartedAt;

                if (effectiveServingStartedAt) {
                  // Calculate based on actual start time and current time
                  const startTime = new Date(effectiveServingStartedAt).getTime();
                  const currentTime = new Date().getTime();
                  const elapsedMilliseconds = currentTime - startTime;
                  const elapsedSeconds = Math.floor(elapsedMilliseconds / 1000);

                  console.log(`Service time: ${serviceTime} minutes (${serviceTimeSeconds} seconds)`);
                  console.log(`Elapsed time: ${elapsedSeconds} seconds`);
                  console.log(`Started at: ${new Date(effectiveServingStartedAt).toLocaleTimeString()}`);
                  console.log(`Current time: ${new Date().toLocaleTimeString()}`);
                  console.log(`ServingStartedAt sources: servingStatus.servingStartedAt=${servingStatus?.servingStartedAt}, servingStartedAt=${servingStartedAt}`);

                  // Force overtime if the elapsed time is greater than service time
                  // This is the key fix - we're forcing overtime based on real-world time
                  if (elapsedSeconds > serviceTimeSeconds) {
                    isOvertime = true;
                    overtimeSeconds = elapsedSeconds - serviceTimeSeconds;
                    console.log(`In overtime: ${overtimeSeconds} seconds over`);
                  }
                } else {
                  // Fallback to the old calculation if servingStartedAt is not available
                  console.log(`No servingStartedAt available, using fallback calculation`);
                  console.log(`remainingServingTime: ${remainingServingTime}, initialServingTime: ${initialServingTime}`);

                  // Always consider overtime if remainingServingTime is negative
                  if (remainingServingTime < 0) {
                    isOvertime = true;
                    overtimeSeconds = Math.abs(remainingServingTime);
                    console.log(`Overtime based on negative remainingServingTime: ${overtimeSeconds} seconds`);
                  }
                  // Or if we've served longer than the service time
                  else if (initialServingTime > 0 && initialServingTime - remainingServingTime > serviceTimeSeconds) {
                    isOvertime = true;
                    overtimeSeconds = (initialServingTime - remainingServingTime) - serviceTimeSeconds;
                    console.log(`Overtime based on elapsed time: ${overtimeSeconds} seconds`);
                  }
                }

                if (isOvertime) {
                  // Format the overtime in hh:mm:ss format
                  const overtimeHours = Math.floor(overtimeSeconds / 3600);
                  const overtimeMinutes = Math.floor((overtimeSeconds % 3600) / 60);
                  const overtimeRemainingSeconds = Math.floor(overtimeSeconds % 60);

                  return (
                    <View>
                      <Text className="text-danger-600 text-sm font-poppins-medium">
                        Extra Serve time: {overtimeHours.toString().padStart(2, "0")}:{overtimeMinutes.toString().padStart(2, "0")}:{overtimeRemainingSeconds.toString().padStart(2, "0")}
                      </Text>
                    </View>
                  );
                } else {
                  // Normal countdown display in hh:mm:ss format
                  const hours = Math.floor(remainingServingTime / 3600);
                  const minutes = Math.floor((remainingServingTime % 3600) / 60);
                  const seconds = Math.floor(remainingServingTime % 60);

                  return (
                    <Text className="text-secondary-600 text-sm font-poppins-medium">
                      Time Remaining: {hours.toString().padStart(2, "0")}:{minutes.toString().padStart(2, "0")}:{seconds.toString().padStart(2, "0")}
                    </Text>
                  );
                }
              })()}
            </View>
          </View>

          <TouchableOpacity
            className={`bg-primary-500 w-full flex-row justify-center items-center rounded-xl p-4 ${
              isMarkServedLoading ? "opacity-70" : ""
            }`}
            onPress={() => currentMember && markAsCompleted(currentMember.id)}
            disabled={isMarkServedLoading}
          >
            {isMarkServedLoading ? (
              <ActivityIndicator size="small" color="#ffffff" />
            ) : (
              <>
                <Image source={images.check} className="w-4 h-3 mr-3" />
                <Text className="text-white font-poppins-medium">
                  Mark as Served
                </Text>
              </>
            )}
          </TouchableOpacity>
        </View>
      );
    }

    // Show grace period UI if in grace period
    if (inGracePeriod) {
      return (
        <View className="mt-4 flex-col p-6 rounded-3xl border-2 border-primary-500/70">
          <View className="w-full flex-row justify-between items-center mb-6">
            <View className="flex-col items-start">
              <View className="flex-row items-center mb-2">
                <Text className="font-poppins-medium text-lg">
                  {currentMember.fullName || "Anonymous"}
                </Text>
                {currentMember.isVIP && (
                  <View className="ml-2 bg-warning-500 rounded-lg px-2 py-1 flex-row items-center">
                    <Image
                      source={images.vip}
                      className="w-3 h-3 mr-1"
                      tintColor="#ffffff"
                    />
                    <Text className="text-white text-xs font-poppins-medium">
                      VIP
                    </Text>
                  </View>
                )}
              </View>
              {isCheckedIn ? (
                <View className="bg-success-500/10 rounded-lg px-2 py-1 mr-2">
                  <Text className="text-success-500 font-poppins-medium text-xs">
                    Arrived
                  </Text>
                </View>
              ) : (
                <View className="bg-gray-200 rounded-lg px-2 py-1 mr-2">
                  <Text className="text-gray-600 font-poppins-medium text-xs">
                    Not Arrived
                  </Text>
                </View>
              )}
            </View>
            <TouchableOpacity
              className="h-12 w-12 rounded-full flex-row border border-primary-500 justify-center items-center"
              onPress={() => {
                if (currentMember) {
                  callMember(currentMember).catch((error) => {
                    console.error("Error calling member:", error);
                    Alert.alert("Error", "Failed to make the call");
                  });
                }
              }}
            >
              <Image
                source={images.call}
                className="w-5 h-5"
                tintColor="#159AFF"
              />
            </TouchableOpacity>
          </View>
          <View className="flex-row justify-start bg-primary-500/5 border p-4 rounded-2xl border-primary-500/20 items-center">
            <View className="p-2 px-[10px] border-2 border-primary-500 mr-3 rounded-xl">
              <Image
                source={images.clock}
                className="w-7 h-7 mb-1"
                tintColor={"#159AFF"}
              />
            </View>
            <View className="flex-col justify-start items-start">
              <Text className="font-poppins-medium text-primary-500">
                Waiting for Confirmation
              </Text>
              <Text className="font-poppins-regular text-sm text-secondary-600">
                Gracetime: {formatGraceTimeRemaining(remainingGraceTime)}
              </Text>
            </View>
          </View>
        </View>
      );
    }

    // Default case - Show actions based on check-in status
    return (
      <View className="mt-4 p-6 rounded-3xl border-2 border-primary-500/70">
        <View className="w-full flex-row justify-between items-center mb-6">
          <View className="flex-col items-start">
            <View className="flex-row items-center mb-2">
              <Text className="font-poppins-medium text-lg">
                {currentMember.fullName || "Anonymous"}
              </Text>
              {currentMember.isVIP && (
                <View className="ml-2 bg-warning-500 rounded-lg px-2 py-1 flex-row items-center">
                  <Image
                    source={images.vip}
                    className="w-3 h-3 mr-1"
                    tintColor="#ffffff"
                  />
                  <Text className="text-white text-xs font-poppins-medium">
                    VIP
                  </Text>
                </View>
              )}
            </View>
            {isCheckedIn ? (
              <View className="bg-success-500/10 rounded-lg px-2 py-1 mr-2">
                <Text className="text-success-500 font-poppins-medium text-xs">
                  Arrived
                </Text>
              </View>
            ) : (
              <View className="bg-gray-200 rounded-lg px-2 py-1 mr-2">
                <Text className="text-gray-600 font-poppins-medium text-xs">
                  Not Arrived
                </Text>
              </View>
            )}
          </View>
          <TouchableOpacity
            className="h-12 w-12 rounded-full flex-row border border-primary-500 justify-center items-center"
            onPress={() => {
              if (currentMember) {
                callMember(currentMember).catch((error) => {
                  console.error("Error calling member:", error);
                  Alert.alert("Error", "Failed to make the call");
                });
              }
            }}
          >
            <Image
              source={images.call}
              className="w-5 h-5"
              tintColor="#159AFF"
            />
          </TouchableOpacity>
        </View>

        {/* Action Buttons based on check-in status */}
        <View className="flex-row justify-between space-x-4">
          {isCheckedIn ? (
            <>
              <TouchableOpacity
                className={`bg-danger-500 w-[47%] flex-row justify-center items-center rounded-xl p-4 ${
                  isNoShowLoading ? "opacity-70" : ""
                }`}
                onPress={() => currentMember && openNoShowModal(currentMember)}
                disabled={isNoShowLoading}
              >
                {isNoShowLoading ? (
                  <ActivityIndicator size="small" color="#ffffff" />
                ) : (
                  <>
                    <Image
                      source={images.cross}
                      className="w-[13px] h-[13px] mr-3"
                      tintColor={"#ffff"}
                    />
                    <Text className="text-white font-poppins-medium">
                      Not Here
                    </Text>
                  </>
                )}
              </TouchableOpacity>
              <TouchableOpacity
                className={`bg-[#10B981] w-[47%] flex-row justify-center items-center rounded-xl p-4 ${
                  isStartingService ? "opacity-70" : ""
                }`}
                onPress={() => currentMember && startServing(currentMember.id)}
                disabled={isStartingService}
              >
                {isStartingService ? (
                  <ActivityIndicator size="small" color="#ffffff" />
                ) : (
                  <>
                    <Image
                      source={images.play}
                      className="w-[14px] h-[15px] mr-2"
                    />
                    <Text className="text-white font-poppins-medium">
                      Start Serving
                    </Text>
                  </>
                )}
              </TouchableOpacity>
            </>
          ) : memberStatus === "waiting" && !isCheckedIn ? (

            <TouchableOpacity
              className={`bg-warning-500 w-full flex-row justify-center items-center rounded-xl p-4 ${
                isMarkServedLoading ? "opacity-70" : ""
              }`}
              onPress={() => currentMember && startGracePeriod(currentMember.id)}
              disabled={isMarkServedLoading}
            >
              {isMarkServedLoading ? (
                <ActivityIndicator size="small" color="#ffffff" />
              ) : (
                <>
                  <Image
                    source={images.clock}
                    className="w-5 h-5 mr-3"
                    tintColor={"#ffff"}
                  />
                  <Text className="text-white font-poppins-medium">
                    Start Grace Period
                  </Text>
                </>
              )}
            </TouchableOpacity>

          ) : (
            // Fallback - showing a waiting message
            <View className="w-full flex-row justify-center items-center rounded-xl p-4 bg-gray-100">
              <Text className="text-secondary-500 font-poppins-medium">
                Waiting for customer status update...
              </Text>
            </View>
          )}
        </View>
      </View>
    );
  };

  // Update the Currently Serving section of the render function with persistent visibility
  const CurrentlyServingSection = () => {
    const currentMember = overview?.currentlyServing;

    // If no current member, don't render anything
    if (!currentMember) return null;

    return (
      <View className="mb-8 w-full justify-center items-center">
        <View className="bg-secondary-500/20 w-full h-[1px] mb-6" />
        <Text className="text-secondary-600 text-base mb-2 font-poppins-medium">
          Currently Serving
        </Text>
        {/* Using only member ID as key to prevent unwanted re-renders */}
        <View key={`serving-${currentMember.id}`} style={{opacity: 1}}>
          <ServingActionsCard />
        </View>
      </View>
    );
  };

  // Now render conditionally without early return
  return (
    <SafeAreaView className="flex-1 bg-white">
      <StatusBar barStyle="dark-content" backgroundColor="white" />

      <Stack.Screen
        options={{
          headerShown: false,
        }}
      />

      {/* Verification Modal */}
      <Modal
        animationType="none"
        transparent={true}
        visible={verificationModalVisible}
        onRequestClose={() => {
          closeModalWithAnimation();
          // Ensure loading states are reset when modal is closed by back button too
          setIsStartingService(false);
          setIsVerifyLoading(false);
        }}
      >
        <Animated.View
          className="flex-1 justify-center items-center bg-black/50 px-16"
          style={{ opacity: fadeAnim }}
        >
          <Animated.View
            className="bg-white rounded-3xl p-8 w-full items-center"
            style={{
                opacity: fadeAnim,
                transform: [{ scale: scaleAnim }],
            }}
          >
            <Text className="text-lg font-poppins-medium text-secondary-800 mb-2">
              {isStartingService ? "Verify Unique Slot ID" : "Complete Service"}
            </Text>
            <Text className="text-sm font-poppins-regular text-secondary-600 text-center mb-5">
              {isStartingService
                ? "Please enter the 4-digit code from customer's unique slot ID"
                : "Please confirm you want to mark this service as completed"}
            </Text>

            {isStartingService && (
              <>
                <View className="flex-row gap-4 mb-5 justify-center items-center w-full">
                  {[...Array(4)].map((_, index) => (
              <TextInput
                      key={index}
                      ref={(ref) => (inputRefs.current[index] = ref)}
                      className={`h-[55px] w-[50px] border rounded-xl text-lg font-poppins-medium text-center ${
                        invalidInput
                          ? "border-danger-500"
                          : verificationSlotId[index]
                            ? "border-primary-500"
                            : "border-secondary-400"
                      }`}
                      value={verificationSlotId[index] || ""}
                      onChangeText={(value) => handleDigitChange(index, value)}
                keyboardType="phone-pad"
                      maxLength={1}
                editable={!isVerifyLoading}
                      caretHidden={false}
                      selectTextOnFocus={false}
                      onFocus={() => {
                        // Reset validation when focused
                        if (invalidInput) {
                          setInvalidInput(false);
                        }
                      }}
                    />
                  ))}
                </View>
              </>
            )}

            {verificationError ? (
              <Text className="text-danger-500 text-sm font-poppins-regular mb-3 text-center">
                {verificationError}
              </Text>
            ) : null}

            <View className="flex-row justify-between w-full mt-3">
              <TouchableOpacity
                className={`flex-1 p-3 py-6 rounded-2xl bg-gray-100 items-center mr-2 ${
                  isVerifyLoading ? "opacity-70" : ""
                }`}
                onPress={closeModalWithAnimation}
                disabled={isVerifyLoading}
              >
                <Text className="text-secondary-600 font-poppins-medium text-sm">
                  Cancel
                </Text>
              </TouchableOpacity>

              <TouchableOpacity
                className={`flex-1 p-3  py-6 rounded-2xl bg-primary-500 items-center ml-2 ${
                  isVerifyLoading || (isStartingService && verificationSlotId.length < 4) ? "opacity-70" : ""
                }`}
                onPress={verifyAndCompleteQueue}
                disabled={isVerifyLoading || (isStartingService && verificationSlotId.length < 4)}
              >
                {isVerifyLoading ? (
                  <ActivityIndicator size="small" color="#ffffff" />
                ) : (
                  <Text className="text-white font-poppins-medium text-sm">
                    {isStartingService ? "Verify" : "Complete"}
                  </Text>
                )}
              </TouchableOpacity>
            </View>
          </Animated.View>
        </Animated.View>
      </Modal>

      {/* No Show Options Modal */}
      <Modal
        animationType="none"
        transparent={true}
        visible={noShowModalVisible}
        onRequestClose={() => {
          closeNoShowModal();
        }}
      >
        <Animated.View
          className="flex-1 justify-center items-center bg-black/50 px-16"
          style={{ opacity: fadeAnim }}
        >
          <Animated.View
            className="bg-white rounded-3xl p-8 w-full items-center"
            style={{
                opacity: fadeAnim,
                transform: [{ scale: scaleAnim }],
            }}
          >
            <Text className="text-lg font-poppins-medium text-secondary-800 mb-2">
              Customer Not Here
            </Text>
            <Text className="text-sm font-poppins-regular text-secondary-600 text-center mb-5">
              Please select an action for this customer
            </Text>

            <View className="w-full mb-3">
              <TouchableOpacity
                className="bg-danger-500 w-full flex-row justify-center items-center rounded-xl p-4 mb-3"
                onPress={() => noShowQueueMember && markAsNoShow(noShowQueueMember.id)}
                disabled={isNoShowLoading}
              >
                {isNoShowLoading ? (
                  <ActivityIndicator size="small" color="#ffffff" />
                ) : (
                  <>
                    <Image
                      source={images.cross}
                      className="w-[13px] h-[13px] mr-3"
                      tintColor={"#ffff"}
                    />
                    <Text className="text-white font-poppins-medium">
                      Kick out of line
                    </Text>
                  </>
                )}
              </TouchableOpacity>

              <TouchableOpacity
                className="bg-warning-500 w-full flex-row justify-center items-center rounded-xl p-4"
                onPress={() => noShowQueueMember && moveToEndOfLine(noShowQueueMember.id)}
                disabled={isNoShowLoading}
              >
                {isNoShowLoading ? (
                  <ActivityIndicator size="small" color="#ffffff" />
                ) : (
                  <>
                    <Image
                      source={images.clock}
                      className="w-5 h-5 mr-3"
                      tintColor={"#ffff"}
                    />
                    <Text className="text-white font-poppins-medium">
                      Move to last of line
                    </Text>
                  </>
                )}
              </TouchableOpacity>
            </View>

            <TouchableOpacity
              className="p-3 py-4 rounded-2xl bg-gray-100 items-center w-full mt-2"
              onPress={closeNoShowModal}
              disabled={isNoShowLoading}
            >
              <Text className="text-secondary-600 font-poppins-medium text-sm">
                Cancel
              </Text>
            </TouchableOpacity>
          </Animated.View>
        </Animated.View>
      </Modal>

      {isLoading ? (
        <View className="flex-1 justify-center items-center">
          <ActivityIndicator size="large" color="#159AFF" />
        </View>
      ) : (
        <>
      {/* Header */}
      <View className="bg-white pb-4">
        <View className="px-8 pt-12">
          <View className="flex-row w-full justify-start mt-4">
            <TouchableOpacity
              className="flex-row items-center absolute left-0 -top-2 border border-secondary-600 w-12 h-12 rounded-full justify-center"
              onPress={() => router.back()}
            >
              <Image source={images.back} className="w-4 h-4" />
            </TouchableOpacity>

            <View className="flex  items-center w-full">
              <Text className="font-poppins-medium text-xl text-secondary-800 mb-2">
                {timeSlot}
              </Text>

              <View className="flex-row items-center">
                <View className={`px-3   mr-2`}>
                  <Text className={`text-base ${getStatusColor().text}`}>
                    {status === "ongoing"
                      ? "On Live"
                      : status === "ended"
                        ? "Ended"
                        : status === "not-started"
                          ? "Not Started"
                          : status}
                  </Text>
                </View>
              </View>
            </View>
          </View>
        </View>
      </View>

      <ScrollView
        className="flex-1 px-10"
        showsVerticalScrollIndicator={false}
        contentContainerStyle={{ paddingBottom: 100 }}
        refreshControl={
          <RefreshControl
            refreshing={isRefreshing}
            onRefresh={onRefresh}
            colors={["#159AFF"]}
            tintColor="#159AFF"
          />
        }
      >
        {/* Overview Cards */}
        <View className="flex-row justify-center mb-8 mt-6 bg-primary-500/10 rounded-2xl py-5 ">
          <View className="flex-col justify-center items-center">
            <Text className="font-poppins-semibold text-2xl text-primary-500">
              {overview.totalServed}
            </Text>
            <Text className="text-primary-500 text-xs mb-1">Served</Text>
          </View>
          <View className="mx-24 flex-col justify-center items-center">
            <Text className="font-poppins-semibold text-2xl text-secondary-800">
              {overview.pendingServing}
            </Text>
            <Text className="text-secondary-800 text-xs mb-1">Pending</Text>
          </View>
          <View className="flex-col justify-center items-center">
            <Text className="font-poppins-semibold text-2xl text-danger-400">
              {overview.noShows}
            </Text>
            <Text className="text-danger-500 text-xs mb-1">No Shows</Text>
          </View>
        </View>



        {/* Currently Serving - New Implementation */}
            <CurrentlyServingSection />

        {/* Search Input */}
        <View className="mb-6">
          <View className="bg-gray-100 flex-row border border-secondary-600/10 items-center rounded-2xl px-5 py-3">
            <Image source={images.search} className="w-5 h-5 mr-2" />
            <TextInput
              placeholder="Search by name"
              className="flex-1"
              value={searchQuery}
              onChangeText={setSearchQuery}
            />
            {searchQuery.length > 0 && (
              <TouchableOpacity onPress={() => setSearchQuery("")}>
                <Image source={images.close} className="w-6 h-6" />
              </TouchableOpacity>
            )}
          </View>
        </View>

        {/* Tab Buttons */}
        <View className="flex-row mb-6">
          <TouchableOpacity
            className={`flex-1 py-3 ${
              activeTab === "waiting"
                ? "border-b-2 border-primary-500"
                : "border-b border-gray-200"
            }`}
            onPress={() => setActiveTab("waiting")}
          >
            <Text
              className={`text-center font-poppins-medium ${
                activeTab === "waiting"
                  ? "text-primary-500"
                  : "text-secondary-600"
              }`}
            >
              Waiting (
              {overview.currentlyServing
                ? waitingMembers.length - 1
                : waitingMembers.length}
              )
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            className={`flex-1 py-3 ${
              activeTab === "served"
                ? "border-b-2 border-primary-500"
                : "border-b border-gray-200"
            }`}
            onPress={() => setActiveTab("served")}
          >
            <Text
              className={`text-center font-poppins-medium ${
                activeTab === "served"
                  ? "text-primary-500"
                  : "text-secondary-600"
              }`}
            >
              Served ({servedMembers.length})
            </Text>
          </TouchableOpacity>
        </View>

        {/* Member List */}
        {filteredMembers().length === 0 ? (
          <View className="items-center justify-center py-10">
            <Image
              source={images.info}
              className="w-12 h-12 mb-2"
              tintColor="#6B7280"
            />
            <Text className="text-secondary-600 text-center">
              No {activeTab === "waiting" ? "waiting" : "served"} members found.
            </Text>
          </View>
        ) : (
          filteredMembers().map((member, index) => (
            <View key={`member-${member.id}-${member.createdAt}-${index}`}>
              {renderMemberItem({ item: member })}
            </View>
          ))
        )}
      </ScrollView>
        </>
      )}
    </SafeAreaView>
  );
}

// Styles are now defined inline using className
