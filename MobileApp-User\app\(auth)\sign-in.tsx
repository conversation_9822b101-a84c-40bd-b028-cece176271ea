import React from "react";
import {
  StatusBar,
  View,
  Text,
  SafeAreaView,
  Image,
  TextInput,
  Alert,
  TouchableOpacity,
} from "react-native";
import { images } from "@/constants";
import ButtonWhite from "@/components/ButtonWhite";
import { router } from "expo-router";
import axios from "axios";
import { useSignIn, useSignUp, useOAuth, useUser } from "@clerk/clerk-expo";
import * as SecureStore from "expo-secure-store";

const checkLocationAndRedirect = async () => {
  const locationStatus = await SecureStore.getItemAsync("locationStatus");
  const latitude = await SecureStore.getItemAsync("latitude");
  const longitude = await SecureStore.getItemAsync("longitude");
  const locationDescription = await SecureStore.getItemAsync("locationDescription");

  if (!locationStatus || !latitude || !longitude || !locationDescription) {
    return "/(location)/allow-location";
  }
  return "/(root)/(tabs)/home";
};

const SignInScreen = () => {
  const [email, setEmail] = React.useState("");
  const [loading, setLoading] = React.useState(false);
  const { signIn } = useSignIn();
  const { signUp } = useSignUp();
  const { startOAuthFlow } = useOAuth({ strategy: "oauth_google" });
  const { user } = useUser();

  const onSignInOrSignUp = async () => {
    if (!signIn || !signUp) {
      Alert.alert("Error", "Authentication service is not available.");
      return;
    }

    if (!email || !/\S+@\S+\.\S+/.test(email)) {
      Alert.alert("Error", "Please enter a valid email address");
      return;
    }

    try {
      setLoading(true);
      // First attempt to sign in
      try {
        const signInAttempt = await signIn.create({
          identifier: email,
          strategy: "email_code",
        });

        router.push({
          pathname: "/verify-otp",
          params: {
            email: email,
            isSignUp: "false",
          },
        });
      } catch (err: any) {
        // If user doesn't exist, attempt to sign up
        if (err.message === "Couldn't find your account.") {
          const signUpAttempt = await signUp.create({
            emailAddress: email,
          });

          await signUpAttempt.prepareEmailAddressVerification();

          router.push({
            pathname: "/verify-otp",
            params: {
              email: email,
              isSignUp: "true",
            },
          });
        } else {
          throw err; // Re-throw other errors
        }
      }
    } catch (err: any) {
      Alert.alert(
        "Error",
        err.message || "Failed to process your request. Please try again."
      );
    } finally {
      setLoading(false);
    }
  };

  const onSignInWithGoogle = async () => {
    try {
      setLoading(true);
      const { createdSessionId, signIn, signUp, setActive } = await startOAuthFlow();

      if (!createdSessionId || !setActive) {
        throw new Error('OAuth flow failed to complete');
      }

      // First activate the session
      await setActive({ session: createdSessionId });

      if (signUp?.emailAddress) {
        // Construct the full name from first and last name
        const fullName = signUp.firstName && signUp.lastName
          ? `${signUp.firstName} ${signUp.lastName}`.trim()
          : (signUp.firstName || signUp.lastName || '').trim();
        
        const email = signUp.emailAddress;
        
        try {
          console.log("Google sign-up: registering user", {
            email,
            fullName
          });
          
          // We first register the user without the clerkId
          await axios.post(
            "http://***************:3000/api/auth/register-user",
            {
              email,
              fullName
            },
            {
              headers: {
                "Content-Type": "application/json",
                Accept: "application/json",
              },
              timeout: 10000,
            }
          );
          
          // Check if current user is available from Clerk
          if (user && user.id) {
            // Update with clerkId right away
            console.log("Updating clerk ID for Google user:", user.id);
            try {
              await axios.put(
                "http://***************:3000/api/customer/update-clerk-id",
                {
                  email,
                  clerkId: user.id
                },
                {
                  headers: {
                    "Content-Type": "application/json",
                    Accept: "application/json",
                  }
                }
              );
            } catch (clerkIdError) {
              console.error("Failed to update clerkId after Google sign-up:", clerkIdError);
            }
          }
          
        } catch (error: any) {
          console.error("Backend registration error:", error?.response?.data || error);
        }
        
        // Continue with navigation
        const redirectPath = await checkLocationAndRedirect();
        router.replace(redirectPath);
      } else if (signIn) {
        const redirectPath = await checkLocationAndRedirect();
        router.replace(redirectPath);
      }
    } catch (err) {
      console.error("OAuth error:", err);
      Alert.alert("Sign In Error", "Failed to complete sign in. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  return (
    <SafeAreaView className="flex-1 bg-white">
      <StatusBar barStyle="dark-content" backgroundColor="white" />

      <View className="flex-1 px-10 mt-10">
        <Image
          source={images.queue}
          className="w-full h-[320px]"
          resizeMode="contain"
        />

        <View className="mt-8">
          <Text className="font-poppins-medium text-primary-500 text-3xl text-center">
            Claim your
          </Text>
          <Text className="font-poppins-medium text-[35px] text-center">
            Queue Spot Now!
          </Text>
        </View>
      </View>

      <View className="absolute -bottom-0 left-0 right-0 ">
        <Image
          source={images.waves}
          className="w-full h-[500px]"
          resizeMode="cover"
        />

        <View className="absolute bottom-10 left-0 right-0 px-8">
          <View className="flex-row items-center justify-center space-x-5 mb-7">
            <View className="h-[1px] w-28 bg-white" />
            <Text className="font-poppins-regular text-center text-white text-md px-4">
              Log in or Sign up
            </Text>
            <View className="h-[1px] w-28 bg-white" />
          </View>
          <View className="border h-[60px] w-auto border-white rounded-xl mb-8 bg-transparent">
            <TextInput
              className="text-lg font-poppins-regular text-white mx-5 h-full"
              placeholder="Enter your email address"
              placeholderTextColor="rgba(255, 255, 255, 0.8)"
              value={email}
              onChangeText={setEmail}
              keyboardType="email-address"
              autoCapitalize="none"
              cursorColor="white"
            />
          </View>
          <ButtonWhite
            label={loading ? "Please wait..." : "Continue"}
            onPress={onSignInOrSignUp}
            disabled={loading}
          />
          <View className="mt-10">
            <TouchableOpacity
              onPress={onSignInWithGoogle}
              disabled={loading}
              className="h-[50px]  bg-white/20 rounded-xl border-dashed border border-white flex-row items-center justify-center space-x-3 "
              style={{
                opacity: loading ? 0.7 : 1,
              }}
            >
              <Image
                source={images.google}
                className="w-4 h-4 mr-4"
                resizeMode="contain"
              />
              <Text className="font-poppins-medium text-white text-[13px]">
                Continue with Google
              </Text>
            </TouchableOpacity>
          </View>

          <Text className="text-center font-poppins-regular text-white text-xs mt-10 px-4 leading-5">
            By continuing, you agree to our{"\n"}
            <Text className="text-white font-poppins-medium">
              Privacy Policy
            </Text>{" "}
            and{" "}
            <Text className="text-white font-poppins-medium">
              Terms & Condition
            </Text>
          </Text>
        </View>
      </View>
    </SafeAreaView>
  );
};

export default SignInScreen;
