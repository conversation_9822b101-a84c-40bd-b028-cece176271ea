import React, { useState, useEffect } from "react";
import { 
  Text, 
  SafeAreaView, 
  TouchableOpacity, 
  View, 
  StatusBar, 
  Image,
  Pressable,
  Alert,
  ScrollView
} from "react-native";
import { useAuth, useUser } from "@clerk/clerk-expo";
import { router, Redirect } from "expo-router";
import { images } from "@/constants";
import { clearAllSecureStoreData } from '@/utils/secureStore';

const ManageScreen = () => {
  const { signOut, isSignedIn } = useAuth();
  const { user } = useUser();
  const [serviceData, setServiceData] = useState({
    serviceName: "",
    serviceImage: null
  });

  useEffect(() => {
    loadServiceData();
  }, []);

  const loadServiceData = async () => {
    try {
      if (!user?.primaryEmailAddress?.emailAddress) return;
      
      const email = encodeURIComponent(user.primaryEmailAddress.emailAddress);
      const response = await fetch(
        `http://***************:3000/api/partner/service-details/${email}`
      );

      if (response.ok) {
        const data = await response.json();
        console.log('Service details:', data);
        
        setServiceData({
          serviceName: data.serviceName || serviceData.serviceName || user?.fullName || "Business Name",
          serviceImage: data.images?.[0] || null // Use first image from images array
        });
      } else {
        console.error('Failed to fetch service details:', await response.text());
      }
    } catch (error) {
      console.error("Error loading service data:", error);
    }
  };

  const handleSignOut = async () => {
    try {
      Alert.alert(
        "Sign Out",
        "Are you sure you want to sign out?",
        [
          { text: "Cancel", style: "cancel" },
          { 
            text: "Sign Out", 
            onPress: async () => {
              await clearAllSecureStoreData(); // Add this line
              await signOut();
              router.replace("/(auth)/sign-in");
            },
            style: "destructive"
          }
        ]
      );
    } catch (error) {
      console.error("Error signing out:", error);
    }
  };

  if (!isSignedIn) {
    return <Redirect href="/(auth)/sign-in" />;
  }

  return (
    <SafeAreaView className="flex-1 bg-white">
      <StatusBar barStyle="dark-content" backgroundColor="white" />
      <ScrollView 
        showsVerticalScrollIndicator={false}
        contentContainerStyle={{ paddingBottom: 100 }} // Add padding for tab bar
      > 
        <View className="p-6 mt-5">
        
          <View className="items-center mb-8">
            {serviceData.serviceImage ? (
              <Image 
                source={{ uri: serviceData.serviceImage }}
                className="w-24 h-24 rounded-full"
              />
            ) : (
              <View className="w-24 h-24 rounded-full bg-secondary-200 items-center justify-center">
                <Image 
                  source={images.camera}
                  className="w-9 h-8"
                  tintColor="#666666"
                />
              </View>
            )}
            <Text className="font-poppins-semibold text-lg mt-3">
              {serviceData.serviceName}
            </Text>
            <Text className="font-poppins-regular text-secondary-600 text-sm">
              {user?.primaryEmailAddress?.emailAddress}
            </Text>
          </View>

          <View className="space-y-6"> 
            {/* Service Management Section */}
            <View>
              <Text className="font-poppins-regular text-sm text-primary-500 mb-2">Service Management</Text>
              <View className="mb-4 -mx-6">
                <Pressable 
                  className="px-6"
                  android_ripple={{ color: 'rgba(0, 0, 0, 0.1)' }}
                  onPress={() => router.push("/(root)/edit-service")}
                >
                  <View className="flex-row items-center justify-between py-6 border-b border-secondary-100">
                    <View className="flex-row items-center flex-1">
                      <Image source={images.service} className="w-5 h-5 mr-4" />
                      <Text className="font-poppins-regular">Edit Service Details</Text>
                    </View>
                    <Image source={images.down} className="w-[16px] h-[10px] rotate-[-90deg]" tintColor="#455A64" />
                  </View>
                </Pressable>

                <Pressable 
                  className="px-6"
                  android_ripple={{ color: 'rgba(0, 0, 0, 0.1)' }}
                  onPress={() => router.push("/service-setup/setup")}
                >
                  <View className="flex-row items-center justify-between py-6 border-b border-secondary-100">
                    <View className="flex-row items-center flex-1">
                      <Image source={images.setup} className="w-5 h-5 mr-4" />
                      <Text className="font-poppins-regular">Edit Service Setup</Text>
                    </View>
                    <Image source={images.down} className="w-[16px] h-[10px] rotate-[-90deg]" tintColor="#455A64" />
                  </View>
                </Pressable>

                <Pressable 
                  className="px-6"
                  android_ripple={{ color: 'rgba(0, 0, 0, 0.1)' }}
                  onPress={() => router.push("/service-setup/update-leave-days")}
                >
                  <View className="flex-row items-center justify-between py-6 border-b border-secondary-100">
                    <View className="flex-row items-center flex-1">
                      <Image source={images.leave} className="w-[18.9px] h-[18px] mr-4" />
                      <Text className="font-poppins-regular">Update Leave Days</Text>
                    </View>
                    <Image source={images.down} className="w-[16px] h-[10px] rotate-[-90deg]" tintColor="#455A64" />
                  </View>
                </Pressable>

                <Pressable 
                  className="px-6"
                  android_ripple={{ color: 'rgba(0, 0, 0, 0.1)' }}
                  onPress={() => router.push("/service-setup/update-bank-details")}
                >
                  <View className="flex-row items-center justify-between py-6 ">
                    <View className="flex-row items-center flex-1">
                      <Image source={images.bank} className="w-[19px] h-[17px] mr-4" />
                      <Text className="font-poppins-regular">Bank Account Details</Text>
                    </View>
                    <Image source={images.down} className="w-[16px] h-[10px] rotate-[-90deg]" tintColor="#455A64" />
                  </View>
                </Pressable>
              </View>
            </View>

            {/* App Preferences Section */}
            <View>
              <Text className="font-poppins-regular text-sm text-primary-500 mb-2">App Preferences</Text>
              <View className="mb-4 -mx-6">
                <Pressable 
                  className="px-6"
                  android_ripple={{ color: 'rgba(0, 0, 0, 0.1)' }}
                >
                  <View className="flex-row items-center justify-between py-6 border-b border-secondary-100">
                    <View className="flex-row items-center flex-1">
                      <Image source={images.history} className="w-5 h-5 mr-4" />
                      <Text className="font-poppins-regular">Queue History</Text>
                    </View>
                    <Image source={images.down} className="w-[16px] h-[10px] rotate-[-90deg]" tintColor="#455A64" />
                  </View>
                </Pressable>

                <Pressable 
                  className="px-6"
                  android_ripple={{ color: 'rgba(0, 0, 0, 0.1)' }}
                >
                  <View className="flex-row items-center justify-between py-6 border-b border-secondary-100">
                    <View className="flex-row items-center flex-1">
                      <Image source={images.transaction} className="w-5 h-5 mr-4" />
                      <Text className="font-poppins-regular">Transaction History</Text>
                    </View>
                    <Image source={images.down} className="w-[16px] h-[10px] rotate-[-90deg]" tintColor="#455A64" />
                  </View>
                </Pressable>

                <Pressable 
                  className="px-6"
                  android_ripple={{ color: 'rgba(0, 0, 0, 0.1)' }}
                >
                  <View className="flex-row items-center justify-between py-6 border-b border-secondary-100">
                    <View className="flex-row items-center flex-1">
                      <Image source={images.bell} className="w-5 h-5 mr-4" />
                      <Text className="font-poppins-regular">Notifications</Text>
                    </View>
                    <Image source={images.down} className="w-[16px] h-[10px] rotate-[-90deg]" tintColor="#455A64" />
                  </View>
                </Pressable>

                <Pressable 
                  className="px-6"
                  android_ripple={{ color: 'rgba(0, 0, 0, 0.1)' }}
                >
                  <View className="flex-row items-center justify-between py-6">
                    <View className="flex-row items-center flex-1">
                      <Image source={images.language} className="w-5 h-5 mr-4" />
                      <Text className="font-poppins-regular">Language</Text>
                    </View>
                    <Image source={images.down} className="w-[16px] h-[10px] rotate-[-90deg]" tintColor="#455A64" />
                  </View>
                </Pressable>
              </View>
            </View>

            {/* Support & Legal Section */}
            <View>
              <Text className="font-poppins-regular text-sm text-primary-500 mb-2">Support & Legal</Text>
              <View className="-mx-6">
                <Pressable 
                  className="px-6"
                  android_ripple={{ color: 'rgba(0, 0, 0, 0.1)' }}
                >
                  <View className="flex-row items-center justify-between py-6 border-b border-secondary-100">
                    <View className="flex-row items-center flex-1">
                      <Image source={images.help} className="w-5 h-5 mr-4" />
                      <Text className="font-poppins-regular">Help Center</Text>
                    </View>
                    <Image source={images.down} className="w-[16px] h-[10px] rotate-[-90deg]" tintColor="#455A64" />
                  </View>
                </Pressable>

                <Pressable 
                  className="px-6"
                  android_ripple={{ color: 'rgba(0, 0, 0, 0.1)' }}
                >
                  <View className="flex-row items-center justify-between py-6 border-b border-secondary-100">
                    <View className="flex-row items-center flex-1">
                      <Image source={images.privacy} className="w-[16px] h-[20px] mr-4" />
                      <Text className="font-poppins-regular">Privacy Policy</Text>
                    </View>
                    <Image source={images.down} className="w-[16px] h-[10px] rotate-[-90deg]" tintColor="#455A64" />
                  </View>
                </Pressable>

                <Pressable 
                  className="px-6"
                  android_ripple={{ color: 'rgba(0, 0, 0, 0.1)' }}
                >
                  <View className="flex-row items-center justify-between py-6 border-b border-secondary-100">
                    <View className="flex-row items-center flex-1">
                      <Image source={images.terms} className="w-[19.5px] h-[18px] mr-4" />
                      <Text className="font-poppins-regular">Terms & Conditions</Text>
                    </View>
                    <Image source={images.down} className="w-[16px] h-[10px] rotate-[-90deg]" tintColor="#455A64" />
                  </View>
                </Pressable>

                <Pressable 
                  className="px-6"
                  android_ripple={{ color: 'rgba(0, 0, 0, 0.1)' }}
                  onPress={handleSignOut}
                >
                  <View className="flex-row items-center justify-between py-6">
              <View className="flex-row items-center flex-1">
                <Image 
                  source={images.logout} 
                  className="w-[16px] h-[19px] mr-4"
                  tintColor="#E53E3E"
                />
                <Text className="font-poppins-regular text-danger-600">Log Out</Text>
              </View>
              
            </View>
                </Pressable>
              </View>
            </View>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

export default ManageScreen;
