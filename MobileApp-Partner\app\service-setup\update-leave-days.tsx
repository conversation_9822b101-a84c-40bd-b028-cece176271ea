import React, { useState, useEffect, useRef } from "react";
import {
  View,
  Text,
  SafeAreaView,
  TouchableOpacity,
  Image,
  StatusBar,
  Alert,
  ActivityIndicator,
  ScrollView,
} from "react-native";
import { useRouter } from "expo-router";
import { useUser } from "@clerk/clerk-expo";
import * as SecureStore from "expo-secure-store";
import ButtonBlueMain from "@/components/ButtonBlueMain";
import { images } from "@/constants";
import { Calendar } from "react-native-calendars";

interface MarkedDate {
  selected?: boolean;
  selectedColor?: string;
  disabled?: boolean;
  disableTouchEvent?: boolean;
}

interface MarkedDates {
  [date: string]: MarkedDate;
}

interface CalendarDay {
  dateString: string;
  day: number;
  month: number;
  year: number;
  timestamp: number;
}

interface SubUnit {
  name: string;
  id: string;
}

interface SetupData {
  selectedDays: string[];
  timeSlots: any[];
  hasSubUnits?: boolean;
  subUnits?: Array<any>;
}

export default function LeaveDaysScreen() {
  const router = useRouter();
  const { user } = useUser();
  const [isSaving, setIsSaving] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [selectedDates, setSelectedDates] = useState<MarkedDates>({});
  const [nonWorkingDates, setNonWorkingDates] = useState<MarkedDates>({});
  const [serviceId, setServiceId] = useState<string | null>(null);
  const [hasSubUnits, setHasSubUnits] = useState(false);
  const [subUnits, setSubUnits] = useState<SubUnit[]>([]);
  const [selectedSubUnit, setSelectedSubUnit] = useState<string | null>(null);
  const [leaveDaysData, setLeaveDaysData] = useState<{
    main: string[];
    subUnits?: { [key: string]: string[] };
  }>({ main: [] });
  // Add local cache to store UI state changes before saving
  const localSubunitCache = useRef<{ [key: string]: string[] }>({});

  const today = new Date().toISOString().split("T")[0];
  const tomorrow = new Date();
  tomorrow.setDate(tomorrow.getDate() + 1);
  const tomorrowStr = tomorrow.toISOString().split("T")[0];

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setIsLoading(true);
      if (!user?.primaryEmailAddress?.emailAddress) return;

      const email = encodeURIComponent(user.primaryEmailAddress.emailAddress);
      const serviceResponse = await fetch(
        `http://***************:3000/api/partner/service-details/${email}`
      );

      if (!serviceResponse.ok) {
        throw new Error("Failed to fetch service details");
      }

      const serviceData = await serviceResponse.json();
      if (!serviceData.id) return;

      setServiceId(serviceData.id.toString());

      // Load service setup to check for subunits
      await loadServiceSetup(serviceData.id);

      // Load leave days
      await loadExistingLeaveDays(serviceData.id);

      // Load non-working days
      await loadNonWorkingDays(serviceData.id);
    } catch (error) {
      console.error("Error loading data:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const loadServiceSetup = async (serviceId: number) => {
    try {
      const response = await fetch(
        `http://***************:3000/api/partner/service-setup/${serviceId}`
      );

      if (!response.ok) {
        throw new Error("Failed to fetch service setup");
      }

      const result = await response.json();

      if (result?.status === "success" && result.hasSetup && result.data) {
        setHasSubUnits(result.data.hasSubUnits || false);

        if (result.data.hasSubUnits && Array.isArray(result.data.subUnits)) {
          // Map subunits to a simpler format
          const mappedSubUnits = result.data.subUnits.map(
            (unit: any, index: number) => ({
              name: unit.name,
              id: `subunit-${index}`, // Create a unique ID for each subunit
            })
          );

          setSubUnits(mappedSubUnits);

          // Select the first subunit by default
          if (mappedSubUnits.length > 0) {
            setSelectedSubUnit(mappedSubUnits[0].id);
          }
        }
      }
    } catch (error) {
      console.error("Error loading service setup:", error);
    }
  };

  const loadExistingLeaveDays = async (serviceId: number) => {
    try {
      // Try to load leave days
      const leaveResponse = await fetch(
        `http://***************:3000/api/partner/leave-days/${serviceId}`
      );

      if (leaveResponse.ok) {
        const leaveData = await leaveResponse.json();

        // Check if the service has subunits from the response
        const hasSubUnitsFromResponse = leaveData.hasSubUnits === true;

        // Update hasSubUnits state if different
        if (hasSubUnitsFromResponse !== hasSubUnits) {
          setHasSubUnits(hasSubUnitsFromResponse);
        }

        if (leaveData.data) {
          if (hasSubUnitsFromResponse) {
            // For services with subunits, data contains { subUnits: {...} }
            if (
              leaveData.data.subUnits &&
              typeof leaveData.data.subUnits === "object"
            ) {
              // Set the full leaveDays data structure
              setLeaveDaysData({ main: [], subUnits: leaveData.data.subUnits });

              // Initialize local cache with backend data
              localSubunitCache.current = JSON.parse(
                JSON.stringify(leaveData.data.subUnits)
              );

              // If a subunit is selected, update the calendar
              if (selectedSubUnit && leaveData.data.subUnits[selectedSubUnit]) {
                updateSelectedDatesFromArray(
                  leaveData.data.subUnits[selectedSubUnit]
                );
              } else if (Object.keys(leaveData.data.subUnits).length > 0) {
                // If no subunit selected but we have subunits, select the first one
                const firstSubunitId = Object.keys(leaveData.data.subUnits)[0];
                setSelectedSubUnit(firstSubunitId);
                updateSelectedDatesFromArray(
                  leaveData.data.subUnits[firstSubunitId]
                );
              } else {
                // No subunits data, show empty calendar
                setSelectedDates({});
              }
            }
          } else {
            // For services without subunits, data is a direct array of leave days
            if (Array.isArray(leaveData.data)) {
              setLeaveDaysData({ main: leaveData.data, subUnits: {} });
              updateSelectedDatesFromArray(leaveData.data);
            } else {
              // Fallback for old format where data might still be an object with main
              const mainArray = Array.isArray(leaveData.data.main)
                ? leaveData.data.main
                : [];
              setLeaveDaysData({ main: mainArray, subUnits: {} });
              updateSelectedDatesFromArray(mainArray);
            }
          }
        }
      }
    } catch (error) {
      console.error("Error loading leave days:", error);
    }
  };

  const updateSelectedDatesFromArray = (dates: string[]) => {
          const markedDates: MarkedDates = {};

    dates.forEach((date: string) => {
            // Grey out past leave days
            if (date < today) {
        markedDates[date] = {
          selected: true,
          selectedColor: "#D1D5DB",
          disabled: true,
        };
            } else {
        markedDates[date] = { selected: true, selectedColor: "#159AFF" };
            }
          });

          setSelectedDates(markedDates);
  };

  const loadNonWorkingDays = async (serviceId: number) => {
    try {
      // Fetch service setup data to get working days
      const response = await fetch(
        `http://***************:3000/api/partner/service-setup/${serviceId}`
      );

      if (!response.ok) {
        throw new Error("Failed to fetch service setup");
      }

      const result = await response.json();

      if (result?.status === "success" && result.hasSetup && result.data) {
        // Determine if the service uses subunits
        const serviceHasSubUnits = result.data.hasSubUnits === true;

        // Update the hasSubUnits state if needed
        if (serviceHasSubUnits !== hasSubUnits) {
          setHasSubUnits(serviceHasSubUnits);
        }

        // Get selected days based on service type and current selection
        let workingDays: string[] = [];

        if (serviceHasSubUnits && Array.isArray(result.data.subUnits)) {
          // For services with subunits, get the working days of the selected subunit
          if (selectedSubUnit) {
            const subUnitIndex = parseInt(selectedSubUnit.split("-")[1]);
            if (
              result.data.subUnits[subUnitIndex] &&
              Array.isArray(result.data.subUnits[subUnitIndex].selectedDays)
            ) {
              workingDays = result.data.subUnits[subUnitIndex].selectedDays;
            }
          } else if (result.data.subUnits.length > 0) {
            // If no subunit is selected, use the first one
            const firstSubUnit = result.data.subUnits[0];
            if (Array.isArray(firstSubUnit.selectedDays)) {
              workingDays = firstSubUnit.selectedDays;
            }

            // Update the selected subunit if not set
            if (!selectedSubUnit) {
              setSelectedSubUnit(`subunit-0`);
            }
          }
        } else {
          // For regular services, use the main working days
          workingDays = Array.isArray(result.data.selectedDays)
            ? result.data.selectedDays
            : [];
        }

        // Calculate non-working days for the next 6 months
        const nonWorkingDatesMap: MarkedDates = {};
        const startDate = new Date();
        const endDate = new Date();
        endDate.setMonth(endDate.getMonth() + 6); // Look 6 months ahead

        // Map day names to day indices (0 = Sunday, 1 = Monday, etc.)
        const dayNameToIndex: { [key: string]: number } = {
          Sunday: 0,
          Monday: 1,
          Tuesday: 2,
          Wednesday: 3,
          Thursday: 4,
          Friday: 5,
          Saturday: 6,
        };

        // Get day indices not in workingDays
        const nonWorkingDayIndices = Object.keys(dayNameToIndex)
          .filter((day) => !workingDays.includes(day))
          .map((day) => dayNameToIndex[day]);

        // Loop through dates and mark non-working days
        for (
          let d = new Date(startDate);
          d <= endDate;
          d.setDate(d.getDate() + 1)
        ) {
          const dayOfWeek = d.getDay(); // 0-6

          if (nonWorkingDayIndices.includes(dayOfWeek)) {
            const dateStr = d.toISOString().split("T")[0];
            nonWorkingDatesMap[dateStr] = {
              disabled: true,
              disableTouchEvent: true,
              selectedColor: "#f0f0f0",
            };
          }
        }

        setNonWorkingDates(nonWorkingDatesMap);
      }
    } catch (error) {
      console.error("Error loading service setup:", error);
    }
  };

  // Save current calendar state to local cache
  const saveCurrentSelectionsToCache = () => {
    if (!hasSubUnits || !selectedSubUnit) return;

    // Get all selected dates (both blue and red)
    const currentSelections = Object.keys(selectedDates).filter(
      (date) => selectedDates[date].selected
    );

    // Update cache with current selections
    localSubunitCache.current[selectedSubUnit] = currentSelections;
  };

  // Handle subunit selection change
  const handleSubUnitChange = (subUnitId: string) => {
    // Save current subunit's selections to cache before switching
    if (selectedSubUnit) {
      saveCurrentSelectionsToCache();
    }

    // Update the selected subunit
    setSelectedSubUnit(subUnitId);

    // Check the local cache first for this subunit
    if (localSubunitCache.current && localSubunitCache.current[subUnitId]) {
      // Use cached data if available
      updateSelectedDatesFromArray(localSubunitCache.current[subUnitId]);
    } else {
      // Fall back to backend data if available
      if (leaveDaysData.subUnits && leaveDaysData.subUnits[subUnitId]) {
        updateSelectedDatesFromArray(leaveDaysData.subUnits[subUnitId]);
        // Also initialize the cache with backend data
        localSubunitCache.current[subUnitId] = [
          ...leaveDaysData.subUnits[subUnitId],
        ];
      } else {
        // No leave days set for this subunit yet, show empty calendar
        setSelectedDates({});
        // Initialize empty cache for this subunit
        localSubunitCache.current[subUnitId] = [];
      }
    }

    // Refresh non-working days for the selected subunit
    if (serviceId) {
      loadNonWorkingDays(parseInt(serviceId));
    }
  };

  // Copy current leave days to all subunits
  const handleCopyToAll = () => {
    if (!hasSubUnits || !selectedSubUnit) return;

    // First, save current selections for the active subunit to cache
    saveCurrentSelectionsToCache();

    // Get only the dates currently selected in the UI with red color
    const currentRedSelections = Object.keys(selectedDates).filter(
      (date) => selectedDates[date].selectedColor === "#F56565"
    );

    if (currentRedSelections.length === 0) {
      Alert.alert(
        "No Selections",
        "Please select dates to copy to other subunits"
      );
      return;
    }

    // Create a copy of the local cache
    const updatedCache = { ...localSubunitCache.current };

    // Update all subunits in the cache
    subUnits.forEach((unit) => {
      if (unit.id !== selectedSubUnit) {
        // Get existing cached dates for this subunit
        const existingCachedDates = updatedCache[unit.id] || [];

        // Get past dates (should be preserved)
        const pastDates = existingCachedDates.filter((date) => date < today);

        // Get future dates that are NOT in the current red selections (to preserve them)
        const existingFutureDates = existingCachedDates.filter(
          (date) => date >= today && !currentRedSelections.includes(date)
        );

        // Combine past dates, existing future dates, and new red selections
        updatedCache[unit.id] = [
          ...pastDates,
          ...existingFutureDates,
          ...currentRedSelections,
        ];
      }
    });

    // Update the local cache
    localSubunitCache.current = updatedCache;

    Alert.alert("Success", "Current selections copied to all subunits", [
      { text: "OK" },
    ]);
  };

  const handleSave = async () => {
    try {
      setIsSaving(true);
      setIsLoading(true);

      if (!serviceId) {
      const email = user?.primaryEmailAddress?.emailAddress;
        if (!email) throw new Error("Email not found");

      const serviceResponse = await fetch(
        `http://***************:3000/api/partner/service-details/${encodeURIComponent(email)}`
      );
      const serviceData = await serviceResponse.json();
        if (!serviceData.id) throw new Error("Service not found");
        setServiceId(serviceData.id.toString());
      }

      // Save current subunit's selections to cache before saving
      if (selectedSubUnit) {
        saveCurrentSelectionsToCache();
      }

      // Prepare the data to send based on whether service has subunits
      let dataToSend: any;

      if (hasSubUnits) {
        // For services with subunits, send only the subUnits data from our cache
        if (!selectedSubUnit) {
          Alert.alert("Error", "Please select a sub-unit");
          setIsSaving(false);
          setIsLoading(false);
          return;
        }

        // Send the complete cache data
        dataToSend = { subUnits: localSubunitCache.current };
      } else {
        // For services without subunits, send just the array directly
        // Get all selected dates from UI
        const currentLeaveDays = Object.keys(selectedDates).filter((date) =>
          date < today ? selectedDates[date].selectedColor === "#D1D5DB" : true
        );
        dataToSend = currentLeaveDays;
      }

      const response = await fetch(
        `http://***************:3000/api/partner/leave-days/${serviceId}`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(dataToSend),
        }
      );

      if (!response.ok) throw new Error("Failed to save leave days");

      router.back();
    } catch (error) {
      console.error(error);
      Alert.alert("Error", "Failed to save leave days");
    } finally {
      setIsSaving(false);
      setIsLoading(false);
    }
  };

  // Handle day selection on calendar
  const handleDayPress = (day: CalendarDay) => {
    // Prevent selection of today, past dates, or non-working days
    if (day.dateString < tomorrowStr || nonWorkingDates[day.dateString]) {
      return;
    }

    const updatedDates = { ...selectedDates };
    if (updatedDates[day.dateString]) {
      delete updatedDates[day.dateString];
    } else {
      updatedDates[day.dateString] = {
        selected: true,
        selectedColor: "#F56565",
      };
    }
    setSelectedDates(updatedDates);

    // Note: We don't update the cache here immediately, only when switching subunits or saving
  };

  // Combine leave days and non-working days for the calendar
  const combinedMarkedDates = {
    ...nonWorkingDates,
    ...selectedDates,
  };

  return (
    <SafeAreaView className="flex-1 bg-white">
      <StatusBar barStyle="dark-content" backgroundColor="white" />

      {isLoading && (
        <View className="absolute w-full h-full bg-black/30 z-50 items-center justify-center">
          <View className="bg-white p-8 rounded-xl items-center">
            <ActivityIndicator size="large" color="#159AFF" className="mb-2" />
            <Text className="font-poppins-medium text-secondary-500 text-base">
              Loading...
            </Text>
          </View>
        </View>
      )}
      
      <View className="px-8 pt-8">
        <View className="w-full flex-row ">
          <TouchableOpacity
            className="flex-row items-center absolute left-0 border border-secondary-600 w-12 h-12 rounded-full justify-center"
            onPress={() => router.back()}
          >
            <Image source={images.back} className="w-4 h-4" />
          </TouchableOpacity>
          <View className="flex-1 items-center mt-2 mb-8">
            <Text className="font-poppins-medium text-xl">Set Leave Days</Text>
          </View>
        </View>
      </View>

      {/* SubUnit Selection */}
      {hasSubUnits && subUnits.length > 0 && (
        <View className="mx-6 mb-4">
          <View className="flex-row justify-between items-center mb-2">
            <Text className="font-poppins-medium text-secondary-500">
              Select Sub-Unit
            </Text>
          </View>
          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            className="mb-2"
          >
            {/* SubUnit options */}
            {subUnits.map((unit) => (
              <TouchableOpacity
                key={unit.id}
                onPress={() => handleSubUnitChange(unit.id)}
                className={`mr-4 px-4 py-3 rounded-2xl ${selectedSubUnit === unit.id ? "bg-primary-500" : "bg-gray-200"}`}
              >
                <Text
                  className={`font-poppins-medium ${selectedSubUnit === unit.id ? "text-white" : "text-secondary-600"}`}
                >
                  {unit.name}
                </Text>
              </TouchableOpacity>
            ))}
          </ScrollView>
        </View>
      )}

      <Calendar
        onDayPress={handleDayPress}
        markedDates={combinedMarkedDates}
        minDate={tomorrowStr} // Set minimum date to tomorrow
        hideExtraDays={true}
        theme={{
          textDisabledColor: "#D1D5DB",
          monthTextColor: "#159AFF",
          textMonthFontFamily: "Poppins-Medium",
          textDayHeaderFontFamily: "Poppins-Regular",
          textDayFontFamily: "Poppins-Regular",
          textMonthFontSize: 16,
          dayTextColor: "#455A64",
          "stylesheet.calendar.main": {
            disabledText: {
              color: "#D1D5DB",
              opacity: 0.4,
            },
          },
        }}
      />

      {/* Copy to All button - only show when there are multiple subunits */}
      {subUnits.length > 1 && selectedSubUnit && (
        <TouchableOpacity
          onPress={handleCopyToAll}
          className="bg-blue-500/10 self-end px-3 py-3 mt-8 rounded-lg flex-row items-center justify-center mx-6"
        >
          <Image
            source={images.copy}
            className="w-5 h-5 mr-1"
            tintColor="#159AFF"
          />
          <Text className="font-poppins-medium text-primary-500 text-sm">
            Copy to All
          </Text>
        </TouchableOpacity>
      )}

      {/* Info Box */}
      <View className="mx-6 my-4 absolute bottom-32 bg-secondary-500/5 p-4 rounded-xl flex-row items-start">
        <Image 
          source={images.info} 
          className="w-5 h-5 mr-3 mt-0.5" 
          tintColor="#263238"
        />
        <View className="flex-1">
          <Text className="text-sm font-poppins-medium text-secondary-500 mb-1">
            Monthly Leave Update Required
          </Text>
          <Text className="text-xs text-secondary-600">
            Please update your leave schedule for each upcoming month at least 7
            days before the month begins. You'll receive a monthly reminder to
            help you stay on time.
          </Text>
        </View>
      </View>

      <View className="p-4 border-t absolute bottom-0 w-full justify-center items-center border-gray-200">
        <ButtonBlueMain
          label={isSaving ? "Saving..." : "Save Leave Days"}
          onPress={handleSave}
          bgVariant="primary"
          textVariant="primary"
          className="w-[350px] h-[80px]"
          disabled={isSaving}
        />
      </View>
    </SafeAreaView>
  );
}
