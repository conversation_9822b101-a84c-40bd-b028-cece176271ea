{"name": "@expo/metro-runtime", "version": "4.0.1", "description": "Tools for making advanced Metro bundler features work", "sideEffects": true, "main": "src", "types": "build", "homepage": "https://github.com/expo/expo/tree/main/packages/@expo/metro-runtime", "keywords": [], "author": "650 Industries, Inc.", "license": "MIT", "files": ["build", "src", "rsc", "symbolicate", "async-require.js", "async-require.d.ts", "error-overlay.js", "error-overlay.d.ts", "assets", "!**/__tests__"], "scripts": {"build": "expo-module build", "clean": "expo-module clean", "lint": "expo-module lint", "test": "expo-module test", "prepare": "expo-module prepare", "prepublishOnly": "expo-module prepublishOnly", "expo-module": "expo-module"}, "repository": {"type": "git", "url": "https://github.com/expo/expo.git"}, "peerDependencies": {"react-native": "*"}, "gitHead": "0a0c6d35a691fc59c0d94675d159ac9e3dfd6f26"}